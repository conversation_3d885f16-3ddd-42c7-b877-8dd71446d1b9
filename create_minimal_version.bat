@echo off
echo 🔧 建立最小化版本
echo =====================================

echo.
echo 📋 第1步：備份原始build.gradle.kts...
copy "app\build.gradle.kts" "app\build.gradle.kts.backup"

echo.
echo 📋 第2步：使用最小化build.gradle.kts...
copy "app\build_minimal.gradle.kts" "app\build.gradle.kts"

echo.
echo 📋 第3步：暫時移除可能有問題的檔案...
if exist "app\src\main\java\com\expensetracker\data" (
    echo 🗑️  暫時移除data資料夾...
    move "app\src\main\java\com\expensetracker\data" "app\src\main\java\com\expensetracker\data_backup"
)

if exist "app\src\main\java\com\expensetracker\di" (
    echo 🗑️  暫時移除di資料夾...
    move "app\src\main\java\com\expensetracker\di" "app\src\main\java\com\expensetracker\di_backup"
)

echo.
echo 📋 第4步：清理並建置...
gradlew.bat clean
gradlew.bat assembleDebug

if %errorlevel% equ 0 (
    echo ✅ 最小化版本建置成功！
    echo 💡 現在可以逐步加回功能
) else (
    echo ❌ 最小化版本建置失敗
    echo 💡 可能有其他基本問題
)

pause