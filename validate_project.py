#!/usr/bin/env python3
"""
Project validation script for BookKeeping2025
Validates XML files and checks for common build issues
"""
import xml.etree.ElementTree as ET
import os
import re

def check_xml_entities(content, filename):
    """Check for unescaped XML entities"""
    issues = []
    
    # Look for unescaped ampersands
    unescaped_amp = re.findall(r'&(?!amp;|lt;|gt;|quot;|apos;|#\d+;|#x[0-9a-fA-F]+;)', content)
    if unescaped_amp:
        issues.append(f"Unescaped ampersands found: {unescaped_amp}")
    
    return issues

def validate_xml_file(filepath):
    """Validate a single XML file"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for entity issues
        entity_issues = check_xml_entities(content, filepath)
        
        # Parse XML
        ET.parse(filepath)
        
        if entity_issues:
            print(f"⚠️  {filepath} - Valid XML but has entity issues:")
            for issue in entity_issues:
                print(f"    {issue}")
            return False
        else:
            print(f"✅ {filepath} - Valid XML")
            return True
            
    except ET.ParseError as e:
        print(f"❌ {filepath} - XML Parse Error: {e}")
        return False
    except Exception as e:
        print(f"❌ {filepath} - Error: {e}")
        return False

def main():
    """Main validation function"""
    print("🔍 Validating BookKeeping2025 Project...")
    print("=" * 60)
    
    # XML files to check
    xml_files = [
        "app/src/main/res/layout/item_category.xml",
        "app/src/main/res/layout/item_expense.xml", 
        "app/src/main/res/layout/fragment_categories.xml",
        "app/src/main/res/layout/fragment_expenses.xml",
        "app/src/main/res/layout/fragment_home.xml",
        "app/src/main/res/layout/fragment_reports.xml",
        "app/src/main/res/layout/activity_main.xml",
        "app/src/main/res/values/colors.xml",
        "app/src/main/res/values/strings.xml",
        "app/src/main/res/values/themes.xml",
        "app/src/main/res/drawable/circle_background.xml",
        "app/src/main/res/drawable/ic_more_vert_24.xml",
    ]
    
    all_valid = True
    found_files = 0
    
    for xml_file in xml_files:
        if os.path.exists(xml_file):
            found_files += 1
            if not validate_xml_file(xml_file):
                all_valid = False
        else:
            print(f"⚠️  {xml_file} - File not found")
    
    print("=" * 60)
    print(f"📊 Validation Summary:")
    print(f"   Files checked: {found_files}/{len(xml_files)}")
    
    if all_valid and found_files > 0:
        print("🎉 All XML files are valid!")
        print("✅ Project should build successfully!")
        return 0
    else:
        print("💥 Some issues found!")
        return 1

if __name__ == "__main__":
    exit(main())