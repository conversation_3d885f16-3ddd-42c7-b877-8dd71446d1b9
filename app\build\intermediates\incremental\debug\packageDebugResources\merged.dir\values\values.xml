<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent_orange">#FFFF9800</color>
    <color name="accent_orange_dark">#FFF57C00</color>
    <color name="background_dark">#FF121212</color>
    <color name="background_light">#FFFAFAFA</color>
    <color name="black">#FF000000</color>
    <color name="category_education">#FF3F51B5</color>
    <color name="category_entertainment">#FF9C27B0</color>
    <color name="category_food">#FFFF5722</color>
    <color name="category_health">#FF4CAF50</color>
    <color name="category_other">#FF795548</color>
    <color name="category_shopping">#FFFF9800</color>
    <color name="category_transport">#FF2196F3</color>
    <color name="category_utilities">#FF607D8B</color>
    <color name="error_red">#FFF44336</color>
    <color name="info_blue">#FF2196F3</color>
    <color name="primary_green">#FF4CAF50</color>
    <color name="primary_green_dark">#FF388E3C</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="success_green">#FF4CAF50</color>
    <color name="surface_dark">#FF1E1E1E</color>
    <color name="surface_light">#FFFFFFFF</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="warning_yellow">#FFFFC107</color>
    <color name="white">#FFFFFFFF</color>
    <string name="action_settings">Settings</string>
    <string name="add">Add</string>
    <string name="add_category">Add Category</string>
    <string name="add_expense">Add Expense</string>
    <string name="add_expense_subtitle">Record your expense details</string>
    <string name="app_name">BookKeeping 2025</string>
    <string name="cancel">Cancel</string>
    <string name="category_added">Category added successfully</string>
    <string name="category_breakdown">Category Breakdown</string>
    <string name="category_color">Color</string>
    <string name="category_deleted">Category deleted successfully</string>
    <string name="category_name">Category Name</string>
    <string name="category_updated">Category updated successfully</string>
    <string name="confirm">Confirm</string>
    <string name="delete">Delete</string>
    <string name="delete_category">Delete Category</string>
    <string name="delete_expense">Delete Expense</string>
    <string name="edit">Edit</string>
    <string name="edit_category">Edit Category</string>
    <string name="edit_expense">Edit Expense</string>
    <string name="error_amount_invalid">Please enter a valid amount</string>
    <string name="error_amount_required">Please enter an amount</string>
    <string name="error_category_required">Please select a category</string>
    <string name="error_description_required">Please enter a description</string>
    <string name="error_description_too_short">Description must be at least 2 characters</string>
    <string name="error_empty_amount">Please enter an amount</string>
    <string name="error_empty_category_name">Please enter a category name</string>
    <string name="error_empty_description">Please enter a description</string>
    <string name="error_invalid_amount">Please enter a valid amount</string>
    <string name="expense_added">Expense added successfully</string>
    <string name="expense_amount">Amount</string>
    <string name="expense_category">Category</string>
    <string name="expense_created_success">Expense created successfully</string>
    <string name="expense_creation_error">Error creating expense</string>
    <string name="expense_date">Date</string>
    <string name="expense_deleted">Expense deleted successfully</string>
    <string name="expense_description">Description</string>
    <string name="expense_location">Location</string>
    <string name="expense_notes">Notes</string>
    <string name="expense_updated">Expense updated successfully</string>
    <string name="monthly_report">Monthly Report</string>
    <string name="no_expenses_message">No expenses yet. Add your first expense!</string>
    <string name="optional_details">Optional Details</string>
    <string name="payment_bank_transfer">Bank Transfer</string>
    <string name="payment_cash">Cash</string>
    <string name="payment_check">Check</string>
    <string name="payment_credit_card">Credit Card</string>
    <string name="payment_debit_card">Debit Card</string>
    <string name="payment_digital_wallet">Digital Wallet</string>
    <string name="payment_method">Payment Method</string>
    <string name="payment_other">Other</string>
    <string name="quick_actions">Quick Actions</string>
    <string name="recent_expenses">Recent Expenses</string>
    <string name="save">Save</string>
    <string name="saving">Saving...</string>
    <string name="this_month_label">This Month</string>
    <string name="title_categories">Categories</string>
    <string name="title_expenses">Expenses</string>
    <string name="title_home">Home</string>
    <string name="title_reports">Reports</string>
    <string name="total_expenses">Total Expenses</string>
    <string name="total_expenses_label">Total Expenses</string>
    <string name="view_all">View All</string>
    <string name="welcome_subtitle">Track your expenses and manage your finances</string>
    <string name="welcome_title">Welcome to BookKeeping 2025</string>
    <string name="yearly_report">Yearly Report</string>
    <style name="AppButton" parent="Widget.Material3.Button">
        <item name="android:layout_height">48dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textAllCaps">false</item>
    </style>
    <style name="AppButton.Primary" parent="AppButton">
        <item name="backgroundTint">@color/primary_green</item>
        <item name="android:textColor">@color/white</item>
    </style>
    <style name="AppButton.Secondary" parent="AppButton">
        <item name="backgroundTint">@color/accent_orange</item>
        <item name="android:textColor">@color/white</item>
    </style>
    <style name="AppCard" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">8dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:layout_margin">8dp</item>
    </style>
    <style name="AppTextInputLayout" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary_green</item>
        <item name="hintTextColor">@color/primary_green</item>
    </style>
    <style name="Theme.BookKeeping2025" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/primary_green</item>
        <item name="colorPrimaryVariant">@color/primary_green_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/accent_orange</item>
        <item name="colorSecondaryVariant">@color/accent_orange_dark</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style>
    <style name="Theme.BookKeeping2025.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/>
    <style name="Theme.BookKeeping2025.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.BookKeeping2025.PopupOverlay" parent="ThemeOverlay.AppCompat.Light"/>
</resources>