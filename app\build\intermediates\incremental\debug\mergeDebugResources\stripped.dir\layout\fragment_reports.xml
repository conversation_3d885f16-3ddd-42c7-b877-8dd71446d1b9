<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".ui.reports.ReportsFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Section -->
        <com.google.android.material.card.MaterialCardView
            style="@style/AppCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Expense Reports"
                    android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                    android:textColor="?attr/colorPrimary" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="Analyze your spending patterns and trends"
                    android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                    android:textColor="?attr/colorOnSurfaceVariant" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Summary Cards -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="horizontal"
            android:weightSum="3">

            <!-- Total Expenses -->
            <com.google.android.material.card.MaterialCardView
                style="@style/AppCard"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Total"
                        android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                        android:textColor="?attr/colorOnSurfaceVariant" />

                    <TextView
                        android:id="@+id/text_total_expenses"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="$0.00"
                        android:textAppearance="@style/TextAppearance.Material3.TitleLarge"
                        android:textColor="?attr/colorPrimary" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- This Month -->
            <com.google.android.material.card.MaterialCardView
                style="@style/AppCard"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="4dp"
                android:layout_weight="1">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="This Month"
                        android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                        android:textColor="?attr/colorOnSurfaceVariant" />

                    <TextView
                        android:id="@+id/text_monthly_expenses"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="$0.00"
                        android:textAppearance="@style/TextAppearance.Material3.TitleLarge"
                        android:textColor="?attr/colorSecondary" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Average -->
            <com.google.android.material.card.MaterialCardView
                style="@style/AppCard"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_weight="1">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Average"
                        android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                        android:textColor="?attr/colorOnSurfaceVariant" />

                    <TextView
                        android:id="@+id/text_average_expenses"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="$0.00"
                        android:textAppearance="@style/TextAppearance.Material3.TitleLarge"
                        android:textColor="?attr/colorTertiary" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

        <!-- Monthly Trend Chart -->
        <com.google.android.material.card.MaterialCardView
            style="@style/AppCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Monthly Trend"
                    android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                    android:textColor="?attr/colorOnSurface" />

                <!-- Placeholder for chart -->
                <com.github.mikephil.charting.charts.LineChart
                    android:id="@+id/chart_monthly_trend"
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:layout_marginTop="16dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Category Breakdown Chart -->
        <com.google.android.material.card.MaterialCardView
            style="@style/AppCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Category Breakdown"
                    android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                    android:textColor="?attr/colorOnSurface" />

                <!-- Placeholder for pie chart -->
                <com.github.mikephil.charting.charts.PieChart
                    android:id="@+id/chart_category_breakdown"
                    android:layout_width="match_parent"
                    android:layout_height="250dp"
                    android:layout_marginTop="16dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Export Options -->
        <com.google.android.material.card.MaterialCardView
            style="@style/AppCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Export Reports"
                    android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                    android:textColor="?attr/colorOnSurface" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/button_export_pdf"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:layout_weight="1"
                        android:text="Export PDF"
                        app:icon="@drawable/ic_pdf_24" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/button_export_csv"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:layout_weight="1"
                        android:text="Export CSV"
                        app:icon="@drawable/ic_csv_24" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView>