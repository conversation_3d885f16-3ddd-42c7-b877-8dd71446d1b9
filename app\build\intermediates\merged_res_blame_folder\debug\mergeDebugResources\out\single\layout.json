[{"merged": "com.expensetracker.app-mergeDebugResources-48:/layout/item_expense.xml", "source": "com.expensetracker.app-main-51:/layout/item_expense.xml"}, {"merged": "com.expensetracker.app-mergeDebugResources-48:/layout/fragment_add_expense.xml", "source": "com.expensetracker.app-main-51:/layout/fragment_add_expense.xml"}, {"merged": "com.expensetracker.app-mergeDebugResources-48:/layout/fragment_home.xml", "source": "com.expensetracker.app-main-51:/layout/fragment_home.xml"}, {"merged": "com.expensetracker.app-mergeDebugResources-48:/layout/item_category.xml", "source": "com.expensetracker.app-main-51:/layout/item_category.xml"}, {"merged": "com.expensetracker.app-mergeDebugResources-48:/layout/fragment_expenses.xml", "source": "com.expensetracker.app-main-51:/layout/fragment_expenses.xml"}, {"merged": "com.expensetracker.app-mergeDebugResources-48:/layout/activity_main.xml", "source": "com.expensetracker.app-main-51:/layout/activity_main.xml"}, {"merged": "com.expensetracker.app-mergeDebugResources-48:/layout/fragment_categories.xml", "source": "com.expensetracker.app-main-51:/layout/fragment_categories.xml"}, {"merged": "com.expensetracker.app-mergeDebugResources-48:/layout/fragment_reports.xml", "source": "com.expensetracker.app-main-51:/layout/fragment_reports.xml"}]