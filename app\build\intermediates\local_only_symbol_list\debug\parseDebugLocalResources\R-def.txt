R_DEF: Internal format may change without notice
local
color accent_orange
color accent_orange_dark
color background_dark
color background_light
color black
color category_education
color category_entertainment
color category_food
color category_health
color category_other
color category_shopping
color category_transport
color category_utilities
color error_red
color info_blue
color primary_green
color primary_green_dark
color purple_200
color purple_500
color purple_700
color success_green
color surface_dark
color surface_light
color teal_200
color teal_700
color warning_yellow
color white
drawable circle_background
drawable ic_add_24
drawable ic_analytics_24
drawable ic_attach_money_24
drawable ic_calendar_24
drawable ic_category_24
drawable ic_close_24
drawable ic_csv_24
drawable ic_description_24
drawable ic_filter_24
drawable ic_home_24
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_list_24
drawable ic_location_24
drawable ic_more_vert_24
drawable ic_note_24
drawable ic_payment_24
drawable ic_pdf_24
drawable ic_receipt_24
drawable ic_save_24
drawable ic_search_24
drawable ic_sort_24
id bottom_navigation
id button_add_expense
id button_add_first_category
id button_add_first_expense
id button_cancel
id button_export_csv
id button_export_pdf
id button_filter
id button_more_options
id button_save
id button_sort
id button_view_all_expenses
id chart_category_breakdown
id chart_monthly_trend
id dropdown_category
id dropdown_payment_method
id edit_amount
id edit_date
id edit_description
id edit_location
id edit_notes
id edit_search
id fab_add_category
id fab_add_expense
id layout_amount
id layout_category
id layout_date
id layout_description
id layout_empty_state
id layout_location
id layout_notes
id layout_payment_method
id mobile_navigation
id nav_host_fragment_content_main
id navigation_add_expense
id navigation_categories
id navigation_expenses
id navigation_home
id navigation_reports
id recycler_categories
id recycler_expenses
id recycler_recent_expenses
id text_average_expenses
id text_category_name
id text_expense_amount
id text_expense_category
id text_expense_count
id text_expense_date
id text_expense_description
id text_monthly_expenses
id text_no_expenses
id text_total_amount
id text_total_expenses
id toolbar
id view_category_color
layout activity_main
layout fragment_add_expense
layout fragment_categories
layout fragment_expenses
layout fragment_home
layout fragment_reports
layout item_category
layout item_expense
menu bottom_nav_menu
mipmap ic_launcher
mipmap ic_launcher_round
navigation mobile_navigation
string action_settings
string add
string add_category
string add_expense
string add_expense_subtitle
string app_name
string cancel
string category_added
string category_breakdown
string category_color
string category_deleted
string category_name
string category_updated
string confirm
string delete
string delete_category
string delete_expense
string edit
string edit_category
string edit_expense
string error_amount_invalid
string error_amount_required
string error_category_required
string error_description_required
string error_description_too_short
string error_empty_amount
string error_empty_category_name
string error_empty_description
string error_invalid_amount
string expense_added
string expense_amount
string expense_category
string expense_created_success
string expense_creation_error
string expense_date
string expense_deleted
string expense_description
string expense_location
string expense_notes
string expense_updated
string monthly_report
string no_expenses_message
string optional_details
string payment_bank_transfer
string payment_cash
string payment_check
string payment_credit_card
string payment_debit_card
string payment_digital_wallet
string payment_method
string payment_other
string quick_actions
string recent_expenses
string save
string saving
string this_month_label
string title_categories
string title_expenses
string title_home
string title_reports
string total_expenses
string total_expenses_label
string view_all
string welcome_subtitle
string welcome_title
string yearly_report
style AppButton
style AppButton.Primary
style AppButton.Secondary
style AppCard
style AppTextInputLayout
style Theme.BookKeeping2025
style Theme.BookKeeping2025.AppBarOverlay
style Theme.BookKeeping2025.NoActionBar
style Theme.BookKeeping2025.PopupOverlay
xml backup_rules
xml data_extraction_rules
