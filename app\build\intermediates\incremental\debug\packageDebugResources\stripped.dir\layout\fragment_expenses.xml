<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.expenses.ExpensesFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- Search and Filter Section -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/AppTextInputLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Search expenses..."
                    app:startIconDrawable="@drawable/ic_search_24">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edit_search"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                </com.google.android.material.textfield.TextInputLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:orientation="horizontal">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/button_filter"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:layout_weight="1"
                        android:text="Filter"
                        app:icon="@drawable/ic_filter_24" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/button_sort"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:layout_weight="1"
                        android:text="Sort"
                        app:icon="@drawable/ic_sort_24" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Expenses List -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_expenses"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:clipToPadding="false"
            android:paddingHorizontal="16dp"
            android:paddingBottom="80dp"
            tools:listitem="@layout/item_expense" />

        <!-- Empty State -->
        <LinearLayout
            android:id="@+id/layout_empty_state"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="32dp"
            android:visibility="gone">

            <ImageView
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:alpha="0.5"
                android:src="@drawable/ic_receipt_24"
                app:tint="?attr/colorOnSurfaceVariant" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="No expenses yet"
                android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                android:textColor="?attr/colorOnSurfaceVariant" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center"
                android:text="Start tracking your expenses by adding your first expense"
                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                android:textColor="?attr/colorOnSurfaceVariant" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/button_add_first_expense"
                style="@style/AppButton.Primary"
                android:layout_width="wrap_content"
                android:layout_marginTop="24dp"
                android:text="@string/add_expense"
                app:icon="@drawable/ic_add_24" />

        </LinearLayout>

    </LinearLayout>

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_add_expense"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:contentDescription="@string/add_expense"
        app:srcCompat="@drawable/ic_add_24" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>