@echo off
echo 🚀 最終測試 - 無Hilt版本
echo =====================================

echo.
echo 📋 第1步：完全清理...
if exist ".gradle" rmdir /s /q .gradle
if exist "app\build" rmdir /s /q app\build
if exist "build" rmdir /s /q build

echo.
echo 📋 第2步：建置專案...
gradlew.bat clean
gradlew.bat assembleDebug

if %errorlevel% neq 0 (
    echo ❌ 建置失敗
    echo 💡 請檢查錯誤訊息
    pause
    exit /b 1
)

echo ✅ 建置成功！

echo.
echo 📋 第3步：安裝到模擬器...
adb install -r app\build\outputs\apk\debug\app-debug.apk

if %errorlevel% neq 0 (
    echo ❌ 安裝失敗
    pause
    exit /b 1
)

echo ✅ 安裝成功！

echo.
echo 📋 第4步：啟動應用程式...
adb shell am start -n com.expensetracker/.MainActivity

echo.
echo 🎉 測試準備完成！
echo.
echo 📱 請在模擬器中測試：
echo 1. 點擊 Expenses 分頁
echo 2. 點擊 FAB 新增按鈕
echo 3. 確認看到 "FAB clicked!" Toast
echo 4. 確認進入新增記帳頁面
echo 5. 確認看到 "新增記帳頁面載入成功！" Toast
echo 6. 測試取消按鈕返回
echo.

echo 📋 查看日誌：
adb logcat -s ExpensesFragment:D AddExpenseFragment:D

pause