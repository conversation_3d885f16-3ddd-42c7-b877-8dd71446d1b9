#!/usr/bin/env python3
"""
建立一個完全乾淨的版本，只保留基本功能
"""
import os
import shutil

def backup_and_remove_folder(folder_path):
    """備份並移除資料夾"""
    if os.path.exists(folder_path):
        backup_path = folder_path + "_backup"
        if os.path.exists(backup_path):
            shutil.rmtree(backup_path)
        shutil.move(folder_path, backup_path)
        print(f"✅ 已備份 {folder_path} 到 {backup_path}")
        return True
    return False

def backup_file(file_path):
    """備份檔案"""
    if os.path.exists(file_path):
        backup_path = file_path + ".backup"
        shutil.copy2(file_path, backup_path)
        print(f"✅ 已備份 {file_path}")
        return True
    return False

def main():
    """主要清理流程"""
    print("🧹 建立完全乾淨的版本")
    print("="*50)
    
    # 備份build.gradle.kts
    backup_file("app/build.gradle.kts")
    
    # 使用最小化版本
    if os.path.exists("app/build_minimal.gradle.kts"):
        shutil.copy2("app/build_minimal.gradle.kts", "app/build.gradle.kts")
        print("✅ 已使用最小化build.gradle.kts")
    
    # 暫時移除可能有問題的資料夾
    folders_to_backup = [
        "app/src/main/java/com/expensetracker/data",
        "app/src/main/java/com/expensetracker/di",
        "app/src/main/java/com/expensetracker/ui/adapter"
    ]
    
    for folder in folders_to_backup:
        backup_and_remove_folder(folder)
    
    print("\n✅ 清理完成！")
    print("💡 現在嘗試建置最小化版本")

if __name__ == "__main__":
    main()