package com.expensetracker.ui.reports

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.expensetracker.data.dao.CategoryExpenseSummary
import com.expensetracker.data.dao.MonthlyExpenseSummary
import com.expensetracker.data.repository.ExpenseRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ReportsViewModel @Inject constructor(
    private val expenseRepository: ExpenseRepository
) : ViewModel() {

    private val _monthlyData = MutableLiveData<List<MonthlyExpenseSummary>>()
    val monthlyData: LiveData<List<MonthlyExpenseSummary>> = _monthlyData

    private val _categoryData = MutableLiveData<List<CategoryExpenseSummary>>()
    val categoryData: LiveData<List<CategoryExpenseSummary>> = _categoryData

    val totalExpenses: LiveData<Double> = expenseRepository.getTotalExpensesLiveData().map { it ?: 0.0 }

    private val _isLoading = MutableLiveData<Boolean>().apply {
        value = false
    }
    val isLoading: LiveData<Boolean> = _isLoading

    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage

    init {
        loadReports()
    }

    fun loadReports() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null
                loadMonthlyData()
                loadCategoryData()
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Error loading reports"
            } finally {
                _isLoading.value = false
            }
        }
    }

    private suspend fun loadMonthlyData() {
        try {
            val monthlyData = expenseRepository.getMonthlyExpenseSummary(12)
            _monthlyData.value = monthlyData
        } catch (e: Exception) {
            _monthlyData.value = emptyList()
        }
    }

    private suspend fun loadCategoryData() {
        try {
            val categoryData = expenseRepository.getCategoryExpenseSummary()
            _categoryData.value = categoryData
        } catch (e: Exception) {
            _categoryData.value = emptyList()
        }
    }

    fun refreshReports() {
        loadReports()
    }

    fun clearErrorMessage() {
        _errorMessage.value = null
    }
}