@echo off
echo 🧹 BookKeeping2025 完全清理腳本
echo =====================================

echo.
echo 📋 第1步：停止所有 Gradle 程序...
taskkill /f /im gradle.exe 2>nul
taskkill /f /im java.exe /fi "WINDOWTITLE eq Gradle*" 2>nul
echo ✅ Gradle 程序已停止

echo.
echo 📋 第2步：刪除所有建置快取...
if exist ".gradle" (
    echo 🗑️  刪除 .gradle 資料夾...
    rmdir /s /q .gradle
)
if exist "app\build" (
    echo 🗑️  刪除 app\build 資料夾...
    rmdir /s /q app\build
)
if exist "build" (
    echo 🗑️  刪除 build 資料夾...
    rmdir /s /q build
)

echo.
echo 📋 第3步：清理全域 Gradle 快取...
if exist "%USERPROFILE%\.gradle\caches" (
    echo 🗑️  清理全域 Gradle 快取...
    rmdir /s /q "%USERPROFILE%\.gradle\caches"
)

echo.
echo 📋 第4步：備份並替換 build.gradle.kts...
if exist "app\build.gradle.kts" (
    echo 📦 備份原始 build.gradle.kts...
    copy "app\build.gradle.kts" "app\build.gradle.kts.backup"
)
if exist "app\build_simple.gradle.kts" (
    echo 🔄 使用簡化版本的 build.gradle.kts...
    copy "app\build_simple.gradle.kts" "app\build.gradle.kts"
)

echo.
echo 📋 第5步：嘗試建置簡化版本...
if exist "gradlew.bat" (
    echo 🔧 建置簡化版本...
    gradlew.bat clean assembleDebug
    if %errorlevel% equ 0 (
        echo ✅ 簡化版本建置成功！
        echo 💡 現在可以逐步加回功能
    ) else (
        echo ❌ 簡化版本建置失敗
        echo 💡 可能需要檢查基本環境設定
    )
) else (
    echo ⚠️  找不到 gradlew.bat
)

echo.
echo 📋 清理完成！
echo 💡 如果簡化版本建置成功，可以逐步加回 Room 和 Hilt
pause