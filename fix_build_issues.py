#!/usr/bin/env python3
"""
自動修復建置問題的腳本
"""
import os
import sys
import shutil
import subprocess

def backup_file(file_path):
    """備份檔案"""
    if os.path.exists(file_path):
        backup_path = f"{file_path}.backup"
        shutil.copy2(file_path, backup_path)
        print(f"✅ 已備份 {file_path} 到 {backup_path}")
        return True
    return False

def restore_file(file_path):
    """還原檔案"""
    backup_path = f"{file_path}.backup"
    if os.path.exists(backup_path):
        shutil.copy2(backup_path, file_path)
        print(f"✅ 已還原 {file_path}")
        return True
    return False

def clean_gradle_cache():
    """清理Gradle快取"""
    print("🧹 清理Gradle快取...")
    
    # 清理專案快取
    dirs_to_clean = [".gradle", "app/build", "build"]
    for dir_path in dirs_to_clean:
        if os.path.exists(dir_path):
            shutil.rmtree(dir_path)
            print(f"🗑️  已刪除 {dir_path}")
    
    # 清理全域快取
    gradle_home = os.path.expanduser("~/.gradle")
    if os.path.exists(gradle_home):
        cache_dir = os.path.join(gradle_home, "caches")
        if os.path.exists(cache_dir):
            shutil.rmtree(cache_dir)
            print(f"🗑️  已清理全域Gradle快取")

def create_compatible_build_gradle():
    """建立相容的build.gradle.kts"""
    print("🔧 建立相容的build.gradle.kts...")
    
    compatible_content = '''plugins {
    id("com.android.application")
    id("org.jetbrains.kotlin.android")
    id("kotlin-parcelize")
}

android {
    namespace = "com.expensetracker"
    compileSdk = 34

    defaultConfig {
        applicationId = "com.expensetracker"
        minSdk = 24
        targetSdk = 34
        versionCode = 1
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    
    kotlinOptions {
        jvmTarget = "1.8"
    }
    
    buildFeatures {
        viewBinding = true
    }
}

dependencies {
    implementation("androidx.core:core-ktx:1.12.0")
    implementation("androidx.appcompat:appcompat:1.6.1")
    implementation("com.google.android.material:material:1.11.0")
    implementation("androidx.constraintlayout:constraintlayout:2.1.4")
    
    // Architecture Components
    implementation("androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0")
    implementation("androidx.lifecycle:lifecycle-livedata-ktx:2.7.0")
    implementation("androidx.navigation:navigation-fragment-ktx:2.7.6")
    implementation("androidx.navigation:navigation-ui-ktx:2.7.6")
    
    // RecyclerView
    implementation("androidx.recyclerview:recyclerview:1.3.2")
    
    // Fragment
    implementation("androidx.fragment:fragment-ktx:1.6.2")
    
    // Testing
    testImplementation("junit:junit:4.13.2")
    androidTestImplementation("androidx.test.ext:junit:1.1.5")
    androidTestImplementation("androidx.test.espresso:espresso-core:3.5.1")
}'''
    
    with open("app/build.gradle.kts", "w", encoding="utf-8") as f:
        f.write(compatible_content)
    
    print("✅ 已建立相容的build.gradle.kts")

def create_compatible_root_build_gradle():
    """建立相容的根build.gradle.kts"""
    print("🔧 建立相容的根build.gradle.kts...")
    
    root_content = '''// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    id("com.android.application") version "8.1.4" apply false
    id("org.jetbrains.kotlin.android") version "1.8.22" apply false
}'''
    
    with open("build.gradle.kts", "w", encoding="utf-8") as f:
        f.write(root_content)
    
    print("✅ 已建立相容的根build.gradle.kts")

def create_simple_main_activity():
    """建立簡化的MainActivity"""
    print("🔧 建立簡化的MainActivity...")
    
    # 備份原始檔案
    backup_file("app/src/main/java/com/expensetracker/MainActivity.kt")
    
    # 複製簡化版本
    if os.path.exists("app/src/main/java/com/expensetracker/MainActivity_Simple.kt"):
        shutil.copy2(
            "app/src/main/java/com/expensetracker/MainActivity_Simple.kt",
            "app/src/main/java/com/expensetracker/MainActivity.kt"
        )
        print("✅ 已使用簡化的MainActivity")

def test_build():
    """測試建置"""
    print("🔧 測試建置...")
    
    try:
        if os.path.exists("gradlew.bat"):
            result = subprocess.run(["gradlew.bat", "clean", "assembleDebug"], 
                                  capture_output=True, text=True, timeout=300)
            if result.returncode == 0:
                print("✅ 建置成功！")
                return True
            else:
                print("❌ 建置失敗")
                print("錯誤輸出：")
                print(result.stderr)
                return False
        else:
            print("⚠️  找不到gradlew.bat")
            return False
    except subprocess.TimeoutExpired:
        print("⚠️  建置超時")
        return False
    except Exception as e:
        print(f"❌ 建置過程中發生錯誤: {e}")
        return False

def main():
    """主要修復流程"""
    print("🔧 BookKeeping2025 建置問題自動修復")
    print("="*50)
    
    # 檢查當前目錄
    if not os.path.exists("app/build.gradle.kts"):
        print("❌ 請在BookKeeping2025專案根目錄執行此腳本")
        return 1
    
    try:
        # 步驟1：備份重要檔案
        print("\n📋 步驟1：備份重要檔案...")
        backup_file("app/build.gradle.kts")
        backup_file("build.gradle.kts")
        
        # 步驟2：清理快取
        print("\n📋 步驟2：清理Gradle快取...")
        clean_gradle_cache()
        
        # 步驟3：建立相容配置
        print("\n📋 步驟3：建立相容配置...")
        create_compatible_root_build_gradle()
        create_compatible_build_gradle()
        create_simple_main_activity()
        
        # 步驟4：測試建置
        print("\n📋 步驟4：測試建置...")
        if test_build():
            print("\n🎉 修復成功！")
            print("💡 現在可以逐步加回Room和Hilt功能")
            print("📖 請參考 STEP_BY_STEP_FIX.md 進行下一步")
            return 0
        else:
            print("\n❌ 修復失敗")
            print("🔄 正在還原原始檔案...")
            restore_file("app/build.gradle.kts")
            restore_file("build.gradle.kts")
            restore_file("app/src/main/java/com/expensetracker/MainActivity.kt")
            print("💡 請檢查Android Studio和SDK配置")
            return 1
            
    except Exception as e:
        print(f"\n❌ 修復過程中發生錯誤: {e}")
        return 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n⚠️  修復被使用者中斷")
        sys.exit(1)