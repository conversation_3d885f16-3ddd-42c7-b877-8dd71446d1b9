[{"merged": "com.expensetracker.app-debug-49:/mipmap-hdpi_ic_launcher_round.png.flat", "source": "com.expensetracker.app-main-51:/mipmap-hdpi/ic_launcher_round.png"}, {"merged": "com.expensetracker.app-debug-49:/drawable_ic_sort_24.xml.flat", "source": "com.expensetracker.app-main-51:/drawable/ic_sort_24.xml"}, {"merged": "com.expensetracker.app-debug-49:/layout_fragment_expenses.xml.flat", "source": "com.expensetracker.app-main-51:/layout/fragment_expenses.xml"}, {"merged": "com.expensetracker.app-debug-49:/xml_backup_rules.xml.flat", "source": "com.expensetracker.app-main-51:/xml/backup_rules.xml"}, {"merged": "com.expensetracker.app-debug-49:/mipmap-xxxhdpi_ic_launcher.png.flat", "source": "com.expensetracker.app-main-51:/mipmap-xxxhdpi/ic_launcher.png"}, {"merged": "com.expensetracker.app-debug-49:/drawable_ic_description_24.xml.flat", "source": "com.expensetracker.app-main-51:/drawable/ic_description_24.xml"}, {"merged": "com.expensetracker.app-debug-49:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.expensetracker.app-main-51:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.expensetracker.app-debug-49:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.expensetracker.app-main-51:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.expensetracker.app-debug-49:/navigation_mobile_navigation.xml.flat", "source": "com.expensetracker.app-main-51:/navigation/mobile_navigation.xml"}, {"merged": "com.expensetracker.app-debug-49:/mipmap-xxhdpi_ic_launcher_round.png.flat", "source": "com.expensetracker.app-main-51:/mipmap-xxhdpi/ic_launcher_round.png"}, {"merged": "com.expensetracker.app-debug-49:/mipmap-hdpi_ic_launcher.png.flat", "source": "com.expensetracker.app-main-51:/mipmap-hdpi/ic_launcher.png"}, {"merged": "com.expensetracker.app-debug-49:/drawable_ic_analytics_24.xml.flat", "source": "com.expensetracker.app-main-51:/drawable/ic_analytics_24.xml"}, {"merged": "com.expensetracker.app-debug-49:/drawable_ic_list_24.xml.flat", "source": "com.expensetracker.app-main-51:/drawable/ic_list_24.xml"}, {"merged": "com.expensetracker.app-debug-49:/drawable_ic_csv_24.xml.flat", "source": "com.expensetracker.app-main-51:/drawable/ic_csv_24.xml"}, {"merged": "com.expensetracker.app-debug-49:/layout_fragment_categories.xml.flat", "source": "com.expensetracker.app-main-51:/layout/fragment_categories.xml"}, {"merged": "com.expensetracker.app-debug-49:/drawable_ic_home_24.xml.flat", "source": "com.expensetracker.app-main-51:/drawable/ic_home_24.xml"}, {"merged": "com.expensetracker.app-debug-49:/drawable_ic_note_24.xml.flat", "source": "com.expensetracker.app-main-51:/drawable/ic_note_24.xml"}, {"merged": "com.expensetracker.app-debug-49:/layout_fragment_home.xml.flat", "source": "com.expensetracker.app-main-51:/layout/fragment_home.xml"}, {"merged": "com.expensetracker.app-debug-49:/drawable_ic_close_24.xml.flat", "source": "com.expensetracker.app-main-51:/drawable/ic_close_24.xml"}, {"merged": "com.expensetracker.app-debug-49:/xml_data_extraction_rules.xml.flat", "source": "com.expensetracker.app-main-51:/xml/data_extraction_rules.xml"}, {"merged": "com.expensetracker.app-debug-49:/menu_bottom_nav_menu.xml.flat", "source": "com.expensetracker.app-main-51:/menu/bottom_nav_menu.xml"}, {"merged": "com.expensetracker.app-debug-49:/drawable_ic_payment_24.xml.flat", "source": "com.expensetracker.app-main-51:/drawable/ic_payment_24.xml"}, {"merged": "com.expensetracker.app-debug-49:/drawable_circle_background.xml.flat", "source": "com.expensetracker.app-main-51:/drawable/circle_background.xml"}, {"merged": "com.expensetracker.app-debug-49:/drawable_ic_location_24.xml.flat", "source": "com.expensetracker.app-main-51:/drawable/ic_location_24.xml"}, {"merged": "com.expensetracker.app-debug-49:/drawable_ic_calendar_24.xml.flat", "source": "com.expensetracker.app-main-51:/drawable/ic_calendar_24.xml"}, {"merged": "com.expensetracker.app-debug-49:/drawable_ic_attach_money_24.xml.flat", "source": "com.expensetracker.app-main-51:/drawable/ic_attach_money_24.xml"}, {"merged": "com.expensetracker.app-debug-49:/layout_fragment_add_expense.xml.flat", "source": "com.expensetracker.app-main-51:/layout/fragment_add_expense.xml"}, {"merged": "com.expensetracker.app-debug-49:/drawable_ic_filter_24.xml.flat", "source": "com.expensetracker.app-main-51:/drawable/ic_filter_24.xml"}, {"merged": "com.expensetracker.app-debug-49:/mipmap-xxhdpi_ic_launcher.png.flat", "source": "com.expensetracker.app-main-51:/mipmap-xxhdpi/ic_launcher.png"}, {"merged": "com.expensetracker.app-debug-49:/mipmap-xxxhdpi_ic_launcher_round.png.flat", "source": "com.expensetracker.app-main-51:/mipmap-xxxhdpi/ic_launcher_round.png"}, {"merged": "com.expensetracker.app-debug-49:/mipmap-mdpi_ic_launcher_round.png.flat", "source": "com.expensetracker.app-main-51:/mipmap-mdpi/ic_launcher_round.png"}, {"merged": "com.expensetracker.app-debug-49:/drawable_ic_pdf_24.xml.flat", "source": "com.expensetracker.app-main-51:/drawable/ic_pdf_24.xml"}, {"merged": "com.expensetracker.app-debug-49:/layout_item_category.xml.flat", "source": "com.expensetracker.app-main-51:/layout/item_category.xml"}, {"merged": "com.expensetracker.app-debug-49:/layout_activity_main.xml.flat", "source": "com.expensetracker.app-main-51:/layout/activity_main.xml"}, {"merged": "com.expensetracker.app-debug-49:/mipmap-mdpi_ic_launcher.png.flat", "source": "com.expensetracker.app-main-51:/mipmap-mdpi/ic_launcher.png"}, {"merged": "com.expensetracker.app-debug-49:/mipmap-xhdpi_ic_launcher.png.flat", "source": "com.expensetracker.app-main-51:/mipmap-xhdpi/ic_launcher.png"}, {"merged": "com.expensetracker.app-debug-49:/drawable_ic_receipt_24.xml.flat", "source": "com.expensetracker.app-main-51:/drawable/ic_receipt_24.xml"}, {"merged": "com.expensetracker.app-debug-49:/drawable_ic_save_24.xml.flat", "source": "com.expensetracker.app-main-51:/drawable/ic_save_24.xml"}, {"merged": "com.expensetracker.app-debug-49:/drawable_ic_category_24.xml.flat", "source": "com.expensetracker.app-main-51:/drawable/ic_category_24.xml"}, {"merged": "com.expensetracker.app-debug-49:/mipmap-xhdpi_ic_launcher_round.png.flat", "source": "com.expensetracker.app-main-51:/mipmap-xhdpi/ic_launcher_round.png"}, {"merged": "com.expensetracker.app-debug-49:/drawable_ic_launcher_background.xml.flat", "source": "com.expensetracker.app-main-51:/drawable/ic_launcher_background.xml"}, {"merged": "com.expensetracker.app-debug-49:/layout_fragment_reports.xml.flat", "source": "com.expensetracker.app-main-51:/layout/fragment_reports.xml"}, {"merged": "com.expensetracker.app-debug-49:/drawable_ic_search_24.xml.flat", "source": "com.expensetracker.app-main-51:/drawable/ic_search_24.xml"}, {"merged": "com.expensetracker.app-debug-49:/drawable_ic_add_24.xml.flat", "source": "com.expensetracker.app-main-51:/drawable/ic_add_24.xml"}, {"merged": "com.expensetracker.app-debug-49:/drawable_ic_launcher_foreground.xml.flat", "source": "com.expensetracker.app-main-51:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.expensetracker.app-debug-49:/drawable_ic_more_vert_24.xml.flat", "source": "com.expensetracker.app-main-51:/drawable/ic_more_vert_24.xml"}, {"merged": "com.expensetracker.app-debug-49:/layout_item_expense.xml.flat", "source": "com.expensetracker.app-main-51:/layout/item_expense.xml"}]