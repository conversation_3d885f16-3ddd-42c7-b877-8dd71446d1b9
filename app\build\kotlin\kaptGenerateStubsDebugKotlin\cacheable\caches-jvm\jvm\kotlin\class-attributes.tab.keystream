,com.expensetracker.ExpenseTrackerApplicationcom.expensetracker.MainActivity'com.expensetracker.data.dao.CategoryDao-com.expensetracker.data.dao.CategoryWithStats&com.expensetracker.data.dao.ExpenseDao1com.expensetracker.data.dao.MonthlyExpenseSummary2com.expensetracker.data.dao.CategoryExpenseSummary+com.expensetracker.data.database.Converters0com.expensetracker.data.database.ExpenseDatabase:com.expensetracker.data.database.ExpenseDatabase.CompanionKcom.expensetracker.data.database.ExpenseDatabase.Companion.DatabaseCallback'com.expensetracker.data.entity.Category1com.expensetracker.data.entity.Category.Companion&com.expensetracker.data.entity.Expense,com.expensetracker.data.entity.PaymentMethod,com.expensetracker.data.entity.RecurringType2com.expensetracker.data.entity.ExpenseWithCategory5com.expensetracker.data.repository.CategoryRepository4com.expensetracker.data.repository.ExpenseRepository*com.expensetracker.data.util.CurrencyUtils&com.expensetracker.data.util.DateUtils$<EMAIL>,com.expensetracker.ui.adapter.ExpenseAdapter><EMAIL>/com.expensetracker.ui.expenses.ExpensesFragment0com.expensetracker.ui.expenses.ExpensesViewModel'com.expensetracker.ui.home.HomeFragment(com.expensetracker.ui.home.HomeViewModel-com.expensetracker.ui.reports.ReportsFragment.com.expensetracker.ui.reports.ReportsViewModel2com.expensetracker.databinding.ItemCategoryBinding1com.expensetracker.databinding.ItemExpenseBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       