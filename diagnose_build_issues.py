#!/usr/bin/env python3
"""
建置問題診斷腳本
"""
import os
import sys
import subprocess
import json

def check_gradle_files():
    """檢查Gradle配置檔案"""
    print("🔍 檢查 Gradle 配置檔案...")
    
    files_to_check = [
        "build.gradle.kts",
        "app/build.gradle.kts", 
        "settings.gradle.kts",
        "gradle.properties",
        "gradle/wrapper/gradle-wrapper.properties"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {file_path} - 存在")
        else:
            print(f"❌ {file_path} - 缺失")
    
    return True

def check_kotlin_files():
    """檢查Kotlin檔案語法"""
    print("\n🔍 檢查 Kotlin 檔案...")
    
    kotlin_files = []
    for root, dirs, files in os.walk("app/src/main/java"):
        for file in files:
            if file.endswith(".kt"):
                kotlin_files.append(os.path.join(root, file))
    
    print(f"找到 {len(kotlin_files)} 個 Kotlin 檔案")
    
    # 檢查是否有明顯的語法錯誤
    for kotlin_file in kotlin_files:
        try:
            with open(kotlin_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 基本語法檢查
            if 'package ' not in content:
                print(f"⚠️  {kotlin_file} - 缺少 package 宣告")
            
            # 檢查大括號匹配
            open_braces = content.count('{')
            close_braces = content.count('}')
            if open_braces != close_braces:
                print(f"⚠️  {kotlin_file} - 大括號不匹配: {open_braces} 開, {close_braces} 關")
            
        except Exception as e:
            print(f"❌ {kotlin_file} - 讀取錯誤: {e}")
    
    return True

def check_dependencies():
    """檢查依賴版本相容性"""
    print("\n🔍 檢查依賴版本相容性...")
    
    try:
        with open("app/build.gradle.kts", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查關鍵依賴版本
        dependencies_to_check = {
            "kotlin": "1.9.10",
            "room": "2.6.1", 
            "hilt": "2.48",
            "compileSdk": "35"
        }
        
        print("關鍵依賴版本:")
        for dep, expected_version in dependencies_to_check.items():
            if expected_version in content:
                print(f"✅ {dep}: {expected_version}")
            else:
                print(f"⚠️  {dep}: 版本可能不匹配")
        
        # 檢查是否使用KSP
        if "com.google.devtools.ksp" in content:
            print("✅ 使用 KSP (推薦)")
        elif "kotlin-kapt" in content:
            print("⚠️  使用 KAPT (可能有相容性問題)")
        
    except Exception as e:
        print(f"❌ 無法讀取 build.gradle.kts: {e}")
    
    return True

def suggest_solutions():
    """建議解決方案"""
    print("\n💡 建議解決方案:")
    print("="*50)
    
    print("\n🔧 版本相容性問題解決方案:")
    print("1. 降級 Kotlin 版本到 1.9.10")
    print("2. 使用 KSP 替代 KAPT")
    print("3. 確保 Room 版本與 Kotlin 版本相容")
    
    print("\n🧹 清理建置快取:")
    print("1. 執行 clean_and_rebuild.bat")
    print("2. 或在 Android Studio 中:")
    print("   - Build > Clean Project")
    print("   - Build > Rebuild Project")
    
    print("\n📱 替代建置方法:")
    print("1. 使用 Android Studio 建置")
    print("2. 檢查 Android SDK 版本")
    print("3. 確認 Java/Kotlin 版本相容性")
    
    print("\n🔍 進一步診斷:")
    print("1. 查看完整錯誤日誌")
    print("2. 檢查網路連線（依賴下載）")
    print("3. 更新 Android Studio 和 Gradle")

def main():
    """主要診斷流程"""
    print("🔧 BookKeeping2025 建置問題診斷")
    print("="*50)
    
    # 檢查當前目錄
    if not os.path.exists("app/build.gradle.kts"):
        print("❌ 請在 BookKeeping2025 專案根目錄執行此腳本")
        return 1
    
    # 執行各項檢查
    check_gradle_files()
    check_kotlin_files()
    check_dependencies()
    suggest_solutions()
    
    print("\n🎯 診斷完成！")
    print("請根據上述建議修正問題，然後重新建置專案。")
    
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n⚠️  診斷被使用者中斷")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 診斷過程中發生錯誤: {e}")
        sys.exit(1)