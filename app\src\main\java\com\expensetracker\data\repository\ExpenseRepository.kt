package com.expensetracker.data.repository

import androidx.lifecycle.LiveData
import com.expensetracker.data.dao.CategoryExpenseSummary
import com.expensetracker.data.dao.ExpenseDao
import com.expensetracker.data.dao.MonthlyExpenseSummary
import com.expensetracker.data.entity.Expense
import com.expensetracker.data.entity.ExpenseWithCategory
import com.expensetracker.data.entity.PaymentMethod
import java.util.Calendar
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ExpenseRepository @Inject constructor(
    private val expenseDao: ExpenseDao
) {
    
    // LiveData queries
    fun getAllExpenses(): LiveData<List<Expense>> = expenseDao.getAllExpenses()
    
    fun getAllExpensesWithCategory(): LiveData<List<ExpenseWithCategory>> = expenseDao.getAllExpensesWithCategory()
    
    fun getRecentExpensesWithCategory(limit: Int = 10): LiveData<List<ExpenseWithCategory>> = 
        expenseDao.getRecentExpensesWithCategory(limit)
    
    fun getExpensesByCategory(categoryId: Long): LiveData<List<Expense>> = 
        expenseDao.getExpensesByCategory(categoryId)
    
    fun getExpensesWithCategoryByCategory(categoryId: Long): LiveData<List<ExpenseWithCategory>> = 
        expenseDao.getExpensesWithCategoryByCategory(categoryId)
    
    fun getExpensesByDateRange(startDate: Long, endDate: Long): LiveData<List<Expense>> = 
        expenseDao.getExpensesByDateRange(startDate, endDate)
    
    fun getExpensesWithCategoryByDateRange(startDate: Long, endDate: Long): LiveData<List<ExpenseWithCategory>> = 
        expenseDao.getExpensesWithCategoryByDateRange(startDate, endDate)
    
    fun getExpensesByAmountRange(minAmount: Double, maxAmount: Double): LiveData<List<Expense>> = 
        expenseDao.getExpensesByAmountRange(minAmount, maxAmount)
    
    fun getExpensesByPaymentMethod(paymentMethod: PaymentMethod): LiveData<List<Expense>> = 
        expenseDao.getExpensesByPaymentMethod(paymentMethod)
    
    fun searchExpenses(searchQuery: String): LiveData<List<Expense>> = 
        expenseDao.searchExpenses(searchQuery)
    
    fun searchExpensesWithCategory(searchQuery: String): LiveData<List<ExpenseWithCategory>> = 
        expenseDao.searchExpensesWithCategory(searchQuery)
    
    fun getTotalExpensesLiveData(): LiveData<Double?> = expenseDao.getTotalExpensesLiveData()
    
    fun getTotalExpensesByDateRangeLiveData(startDate: Long, endDate: Long): LiveData<Double?> = 
        expenseDao.getTotalExpensesByDateRangeLiveData(startDate, endDate)
    
    // Suspend functions for one-time operations
    suspend fun getAllExpensesSync(): List<Expense> = expenseDao.getAllExpensesSync()
    
    suspend fun getExpenseById(expenseId: Long): Expense? = expenseDao.getExpenseById(expenseId)
    
    suspend fun getExpenseWithCategoryById(expenseId: Long): ExpenseWithCategory? = 
        expenseDao.getExpenseWithCategoryById(expenseId)
    
    suspend fun getTotalExpenses(): Double = expenseDao.getTotalExpenses() ?: 0.0
    
    suspend fun getTotalExpensesByDateRange(startDate: Long, endDate: Long): Double = 
        expenseDao.getTotalExpensesByDateRange(startDate, endDate) ?: 0.0
    
    suspend fun getTotalExpensesByCategory(categoryId: Long): Double = 
        expenseDao.getTotalExpensesByCategory(categoryId) ?: 0.0
    
    suspend fun getExpenseCount(): Int = expenseDao.getExpenseCount()
    
    suspend fun getExpenseCountByDateRange(startDate: Long, endDate: Long): Int = 
        expenseDao.getExpenseCountByDateRange(startDate, endDate)
    
    suspend fun getAverageExpenseAmount(): Double = expenseDao.getAverageExpenseAmount() ?: 0.0
    
    suspend fun getMaxExpenseAmount(): Double = expenseDao.getMaxExpenseAmount() ?: 0.0
    
    suspend fun getMinExpenseAmount(): Double = expenseDao.getMinExpenseAmount() ?: 0.0
    
    suspend fun getMonthlyExpenseSummary(limit: Int = 12): List<MonthlyExpenseSummary> = 
        expenseDao.getMonthlyExpenseSummary(limit)
    
    suspend fun getCategoryExpenseSummary(): List<CategoryExpenseSummary> = 
        expenseDao.getCategoryExpenseSummary()
    
    suspend fun insertExpense(expense: Expense): Long {
        return expenseDao.insertExpense(expense.copy(updatedAt = System.currentTimeMillis()))
    }
    
    suspend fun insertExpenses(expenses: List<Expense>): List<Long> {
        val timestamp = System.currentTimeMillis()
        val updatedExpenses = expenses.map { it.copy(updatedAt = timestamp) }
        return expenseDao.insertExpenses(updatedExpenses)
    }
    
    suspend fun updateExpense(expense: Expense) {
        expenseDao.updateExpense(expense.copy(updatedAt = System.currentTimeMillis()))
    }
    
    suspend fun deleteExpense(expense: Expense) {
        expenseDao.deleteExpense(expense)
    }
    
    suspend fun deleteExpenseById(expenseId: Long) {
        expenseDao.deleteExpenseById(expenseId)
    }
    
    suspend fun deleteExpensesByCategory(categoryId: Long) {
        expenseDao.deleteExpensesByCategory(categoryId)
    }
    
    suspend fun deleteAllExpenses() {
        expenseDao.deleteAllExpenses()
    }
    
    // Helper methods for common date ranges
    suspend fun getCurrentMonthExpenses(): Double {
        val calendar = Calendar.getInstance()
        val startOfMonth = calendar.apply {
            set(Calendar.DAY_OF_MONTH, 1)
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }.timeInMillis
        
        val endOfMonth = calendar.apply {
            set(Calendar.DAY_OF_MONTH, getActualMaximum(Calendar.DAY_OF_MONTH))
            set(Calendar.HOUR_OF_DAY, 23)
            set(Calendar.MINUTE, 59)
            set(Calendar.SECOND, 59)
            set(Calendar.MILLISECOND, 999)
        }.timeInMillis
        
        return getTotalExpensesByDateRange(startOfMonth, endOfMonth)
    }
    
    fun getCurrentMonthExpensesLiveData(): LiveData<Double?> {
        val calendar = Calendar.getInstance()
        val startOfMonth = calendar.apply {
            set(Calendar.DAY_OF_MONTH, 1)
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }.timeInMillis
        
        val endOfMonth = calendar.apply {
            set(Calendar.DAY_OF_MONTH, getActualMaximum(Calendar.DAY_OF_MONTH))
            set(Calendar.HOUR_OF_DAY, 23)
            set(Calendar.MINUTE, 59)
            set(Calendar.SECOND, 59)
            set(Calendar.MILLISECOND, 999)
        }.timeInMillis
        
        return getTotalExpensesByDateRangeLiveData(startOfMonth, endOfMonth)
    }
    
    suspend fun getCurrentWeekExpenses(): Double {
        val calendar = Calendar.getInstance()
        val startOfWeek = calendar.apply {
            set(Calendar.DAY_OF_WEEK, firstDayOfWeek)
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }.timeInMillis
        
        val endOfWeek = calendar.apply {
            add(Calendar.DAY_OF_WEEK, 6)
            set(Calendar.HOUR_OF_DAY, 23)
            set(Calendar.MINUTE, 59)
            set(Calendar.SECOND, 59)
            set(Calendar.MILLISECOND, 999)
        }.timeInMillis
        
        return getTotalExpensesByDateRange(startOfWeek, endOfWeek)
    }
    
    suspend fun createExpense(
        amount: Double,
        description: String,
        categoryId: Long,
        date: Long = System.currentTimeMillis(),
        notes: String? = null,
        location: String? = null,
        paymentMethod: PaymentMethod = PaymentMethod.CASH,
        receiptImagePath: String? = null
    ): Result<Long> {
        return try {
            if (amount <= 0) {
                Result.failure(Exception("Amount must be greater than 0"))
            } else if (description.isBlank()) {
                Result.failure(Exception("Description cannot be empty"))
            } else {
                val expense = Expense(
                    amount = amount,
                    description = description.trim(),
                    categoryId = categoryId,
                    date = date,
                    notes = notes?.trim(),
                    location = location?.trim(),
                    paymentMethod = paymentMethod,
                    receiptImagePath = receiptImagePath
                )
                val id = insertExpense(expense)
                Result.success(id)
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun updateExpense(
        expenseId: Long,
        amount: Double,
        description: String,
        categoryId: Long,
        date: Long,
        notes: String? = null,
        location: String? = null,
        paymentMethod: PaymentMethod = PaymentMethod.CASH,
        receiptImagePath: String? = null
    ): Result<Unit> {
        return try {
            val existingExpense = getExpenseById(expenseId)
                ?: return Result.failure(Exception("Expense not found"))
            
            if (amount <= 0) {
                Result.failure(Exception("Amount must be greater than 0"))
            } else if (description.isBlank()) {
                Result.failure(Exception("Description cannot be empty"))
            } else {
                val updatedExpense = existingExpense.copy(
                    amount = amount,
                    description = description.trim(),
                    categoryId = categoryId,
                    date = date,
                    notes = notes?.trim(),
                    location = location?.trim(),
                    paymentMethod = paymentMethod,
                    receiptImagePath = receiptImagePath
                )
                updateExpense(updatedExpense)
                Result.success(Unit)
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}