@echo off
echo 📱 BookKeeping2025 模擬器測試腳本
echo =====================================

echo.
echo 📋 第1步：檢查模擬器狀態...
adb devices
echo.

echo 📋 第2步：啟動模擬器（如果未運行）...
echo 💡 請手動啟動 Android Studio 中的模擬器，或使用以下命令：
echo emulator -avd Medium_Phone_API_36
echo.

echo 📋 第3步：等待模擬器完全啟動...
echo 請確認模擬器已完全啟動並解鎖
pause

echo.
echo 📋 第4步：安裝應用程式...
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo 🔧 安裝 APK 到模擬器...
    adb install -r app\build\outputs\apk\debug\app-debug.apk
    if %errorlevel% equ 0 (
        echo ✅ 應用程式安裝成功！
    ) else (
        echo ❌ 應用程式安裝失敗
    )
) else (
    echo ❌ 找不到 APK 檔案，請先建置專案
    echo 💡 使用 build_and_test.bat 建置專案
)

echo.
echo 📋 第5步：啟動應用程式...
echo 🔧 啟動 BookKeeping2025...
adb shell am start -n com.expensetracker/.MainActivity

echo.
echo 📋 測試指南：
echo =====================================
echo 1. 📱 在模擬器中開啟 BookKeeping2025 應用程式
echo 2. 🏠 檢查主畫面是否正常顯示
echo 3. 📊 點擊底部導航的 "Expenses" 分頁
echo 4. ➕ 點擊右下角的 FAB 新增按鈕
echo 5. 📝 填寫新增記帳表單：
echo    - 金額：輸入數字（例如：25.99）
echo    - 描述：輸入文字（例如：午餐）
echo    - 分類：選擇一個分類
echo    - 日期：選擇日期
echo    - 付款方式：選擇付款方式
echo 6. 💾 點擊 "Save" 按鈕
echo 7. ✅ 確認記帳記錄成功新增並返回列表
echo.

echo 📋 測試重點：
echo =====================================
echo ✅ 表單驗證：嘗試提交空白或無效資料
echo ✅ 日期選擇：測試日期選擇器功能
echo ✅ 下拉選單：測試分類和付款方式選擇
echo ✅ 取消功能：測試取消按鈕
echo ✅ 成功訊息：確認成功新增後的回饋
echo ✅ 資料顯示：確認新增的記錄出現在列表中
echo.

pause