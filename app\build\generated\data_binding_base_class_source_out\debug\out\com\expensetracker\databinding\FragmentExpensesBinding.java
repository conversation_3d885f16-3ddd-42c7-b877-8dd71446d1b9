// Generated by view binder compiler. Do not edit!
package com.expensetracker.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.expensetracker.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentExpensesBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final MaterialButton buttonAddFirstExpense;

  @NonNull
  public final MaterialButton buttonFilter;

  @NonNull
  public final MaterialButton buttonSort;

  @NonNull
  public final TextInputEditText editSearch;

  @NonNull
  public final FloatingActionButton fabAddExpense;

  @NonNull
  public final LinearLayout layoutEmptyState;

  @NonNull
  public final RecyclerView recyclerExpenses;

  private FragmentExpensesBinding(@NonNull CoordinatorLayout rootView,
      @NonNull MaterialButton buttonAddFirstExpense, @NonNull MaterialButton buttonFilter,
      @NonNull MaterialButton buttonSort, @NonNull TextInputEditText editSearch,
      @NonNull FloatingActionButton fabAddExpense, @NonNull LinearLayout layoutEmptyState,
      @NonNull RecyclerView recyclerExpenses) {
    this.rootView = rootView;
    this.buttonAddFirstExpense = buttonAddFirstExpense;
    this.buttonFilter = buttonFilter;
    this.buttonSort = buttonSort;
    this.editSearch = editSearch;
    this.fabAddExpense = fabAddExpense;
    this.layoutEmptyState = layoutEmptyState;
    this.recyclerExpenses = recyclerExpenses;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentExpensesBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentExpensesBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_expenses, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentExpensesBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_add_first_expense;
      MaterialButton buttonAddFirstExpense = ViewBindings.findChildViewById(rootView, id);
      if (buttonAddFirstExpense == null) {
        break missingId;
      }

      id = R.id.button_filter;
      MaterialButton buttonFilter = ViewBindings.findChildViewById(rootView, id);
      if (buttonFilter == null) {
        break missingId;
      }

      id = R.id.button_sort;
      MaterialButton buttonSort = ViewBindings.findChildViewById(rootView, id);
      if (buttonSort == null) {
        break missingId;
      }

      id = R.id.edit_search;
      TextInputEditText editSearch = ViewBindings.findChildViewById(rootView, id);
      if (editSearch == null) {
        break missingId;
      }

      id = R.id.fab_add_expense;
      FloatingActionButton fabAddExpense = ViewBindings.findChildViewById(rootView, id);
      if (fabAddExpense == null) {
        break missingId;
      }

      id = R.id.layout_empty_state;
      LinearLayout layoutEmptyState = ViewBindings.findChildViewById(rootView, id);
      if (layoutEmptyState == null) {
        break missingId;
      }

      id = R.id.recycler_expenses;
      RecyclerView recyclerExpenses = ViewBindings.findChildViewById(rootView, id);
      if (recyclerExpenses == null) {
        break missingId;
      }

      return new FragmentExpensesBinding((CoordinatorLayout) rootView, buttonAddFirstExpense,
          buttonFilter, buttonSort, editSearch, fabAddExpense, layoutEmptyState, recyclerExpenses);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
