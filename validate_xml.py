#!/usr/bin/env python3
"""
Simple XML validation script to check if our XML files are well-formed
"""
import xml.etree.ElementTree as ET
import os
import sys

def validate_xml_file(file_path):
    """Validate a single XML file"""
    try:
        ET.parse(file_path)
        print(f"✅ {file_path} - Valid XML")
        return True
    except ET.ParseError as e:
        print(f"❌ {file_path} - XML Parse Error: {e}")
        return False
    except Exception as e:
        print(f"❌ {file_path} - Error: {e}")
        return False

def main():
    """Main validation function"""
    xml_files = [
        "app/src/main/res/layout/item_category.xml",
        "app/src/main/res/layout/item_expense.xml",
        "app/src/main/res/layout/fragment_categories.xml",
        "app/src/main/res/layout/fragment_expenses.xml",
        "app/src/main/res/layout/fragment_home.xml",
        "app/src/main/res/layout/fragment_reports.xml",
        "app/src/main/res/layout/activity_main.xml",
        "app/src/main/res/values/colors.xml",
        "app/src/main/res/values/strings.xml",
        "app/src/main/res/values/themes.xml",
    ]
    
    print("🔍 Validating XML files...")
    print("=" * 50)
    
    all_valid = True
    for xml_file in xml_files:
        if os.path.exists(xml_file):
            if not validate_xml_file(xml_file):
                all_valid = False
        else:
            print(f"⚠️  {xml_file} - File not found")
    
    print("=" * 50)
    if all_valid:
        print("🎉 All XML files are valid!")
        return 0
    else:
        print("💥 Some XML files have errors!")
        return 1

if __name__ == "__main__":
    sys.exit(main())