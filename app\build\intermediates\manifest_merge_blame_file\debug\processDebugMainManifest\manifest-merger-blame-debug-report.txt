1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.expensetracker"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <permission
11-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\26d210e50acf91f7815d7153f1eac7d8\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.expensetracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\26d210e50acf91f7815d7153f1eac7d8\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\26d210e50acf91f7815d7153f1eac7d8\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.expensetracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\26d210e50acf91f7815d7153f1eac7d8\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\26d210e50acf91f7815d7153f1eac7d8\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->C:\Users\<USER>\BookKeeping2025\app\src\main\AndroidManifest.xml:5:5-27:19
18        android:name="com.expensetracker.ExpenseTrackerApplication"
18-->C:\Users\<USER>\BookKeeping2025\app\src\main\AndroidManifest.xml:6:9-50
19        android:allowBackup="true"
19-->C:\Users\<USER>\BookKeeping2025\app\src\main\AndroidManifest.xml:7:9-35
20        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
20-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\26d210e50acf91f7815d7153f1eac7d8\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
21        android:dataExtractionRules="@xml/data_extraction_rules"
21-->C:\Users\<USER>\BookKeeping2025\app\src\main\AndroidManifest.xml:8:9-65
22        android:debuggable="true"
23        android:extractNativeLibs="false"
24        android:fullBackupContent="@xml/backup_rules"
24-->C:\Users\<USER>\BookKeeping2025\app\src\main\AndroidManifest.xml:9:9-54
25        android:icon="@drawable/ic_launcher_foreground"
25-->C:\Users\<USER>\BookKeeping2025\app\src\main\AndroidManifest.xml:10:9-56
26        android:label="@string/app_name"
26-->C:\Users\<USER>\BookKeeping2025\app\src\main\AndroidManifest.xml:11:9-41
27        android:roundIcon="@drawable/ic_launcher_foreground"
27-->C:\Users\<USER>\BookKeeping2025\app\src\main\AndroidManifest.xml:12:9-61
28        android:supportsRtl="true"
28-->C:\Users\<USER>\BookKeeping2025\app\src\main\AndroidManifest.xml:13:9-35
29        android:testOnly="true"
30        android:theme="@style/Theme.BookKeeping2025" >
30-->C:\Users\<USER>\BookKeeping2025\app\src\main\AndroidManifest.xml:14:9-53
31        <activity
31-->C:\Users\<USER>\BookKeeping2025\app\src\main\AndroidManifest.xml:16:9-26:20
32            android:name="com.expensetracker.MainActivity"
32-->C:\Users\<USER>\BookKeeping2025\app\src\main\AndroidManifest.xml:17:13-41
33            android:exported="true"
33-->C:\Users\<USER>\BookKeeping2025\app\src\main\AndroidManifest.xml:18:13-36
34            android:label="@string/app_name"
34-->C:\Users\<USER>\BookKeeping2025\app\src\main\AndroidManifest.xml:19:13-45
35            android:theme="@style/Theme.BookKeeping2025.NoActionBar" >
35-->C:\Users\<USER>\BookKeeping2025\app\src\main\AndroidManifest.xml:20:13-69
36            <intent-filter>
36-->C:\Users\<USER>\BookKeeping2025\app\src\main\AndroidManifest.xml:21:13-25:29
37                <action android:name="android.intent.action.MAIN" />
37-->C:\Users\<USER>\BookKeeping2025\app\src\main\AndroidManifest.xml:22:17-69
37-->C:\Users\<USER>\BookKeeping2025\app\src\main\AndroidManifest.xml:22:25-66
38
39                <category android:name="android.intent.category.LAUNCHER" />
39-->C:\Users\<USER>\BookKeeping2025\app\src\main\AndroidManifest.xml:24:17-77
39-->C:\Users\<USER>\BookKeeping2025\app\src\main\AndroidManifest.xml:24:27-74
40            </intent-filter>
41        </activity>
42
43        <provider
43-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4ad3dc040b2784f5d5c6f5a85e4ec10\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
44            android:name="androidx.startup.InitializationProvider"
44-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4ad3dc040b2784f5d5c6f5a85e4ec10\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
45            android:authorities="com.expensetracker.androidx-startup"
45-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4ad3dc040b2784f5d5c6f5a85e4ec10\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
46            android:exported="false" >
46-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4ad3dc040b2784f5d5c6f5a85e4ec10\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
47            <meta-data
47-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4ad3dc040b2784f5d5c6f5a85e4ec10\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
48                android:name="androidx.emoji2.text.EmojiCompatInitializer"
48-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4ad3dc040b2784f5d5c6f5a85e4ec10\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
49                android:value="androidx.startup" />
49-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4ad3dc040b2784f5d5c6f5a85e4ec10\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
50            <meta-data
50-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1d982b11a07ea6f3e207a8c8b2eb86a\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
51                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
51-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1d982b11a07ea6f3e207a8c8b2eb86a\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
52                android:value="androidx.startup" />
52-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1d982b11a07ea6f3e207a8c8b2eb86a\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
53            <meta-data
53-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c34a5c637bc137e9f6d349bb15f7314a\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
54                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
54-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c34a5c637bc137e9f6d349bb15f7314a\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
55                android:value="androidx.startup" />
55-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c34a5c637bc137e9f6d349bb15f7314a\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
56        </provider>
57
58        <uses-library
58-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1faeb4eb8750b08efdcca80f74e874fe\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
59            android:name="androidx.window.extensions"
59-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1faeb4eb8750b08efdcca80f74e874fe\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
60            android:required="false" />
60-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1faeb4eb8750b08efdcca80f74e874fe\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
61        <uses-library
61-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1faeb4eb8750b08efdcca80f74e874fe\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
62            android:name="androidx.window.sidecar"
62-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1faeb4eb8750b08efdcca80f74e874fe\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
63            android:required="false" />
63-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1faeb4eb8750b08efdcca80f74e874fe\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
64
65        <receiver
65-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c34a5c637bc137e9f6d349bb15f7314a\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
66            android:name="androidx.profileinstaller.ProfileInstallReceiver"
66-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c34a5c637bc137e9f6d349bb15f7314a\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
67            android:directBootAware="false"
67-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c34a5c637bc137e9f6d349bb15f7314a\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
68            android:enabled="true"
68-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c34a5c637bc137e9f6d349bb15f7314a\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
69            android:exported="true"
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c34a5c637bc137e9f6d349bb15f7314a\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
70            android:permission="android.permission.DUMP" >
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c34a5c637bc137e9f6d349bb15f7314a\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
71            <intent-filter>
71-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c34a5c637bc137e9f6d349bb15f7314a\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
72                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c34a5c637bc137e9f6d349bb15f7314a\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c34a5c637bc137e9f6d349bb15f7314a\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
73            </intent-filter>
74            <intent-filter>
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c34a5c637bc137e9f6d349bb15f7314a\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
75                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c34a5c637bc137e9f6d349bb15f7314a\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c34a5c637bc137e9f6d349bb15f7314a\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
76            </intent-filter>
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c34a5c637bc137e9f6d349bb15f7314a\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
78                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c34a5c637bc137e9f6d349bb15f7314a\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c34a5c637bc137e9f6d349bb15f7314a\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
79            </intent-filter>
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c34a5c637bc137e9f6d349bb15f7314a\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
81                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c34a5c637bc137e9f6d349bb15f7314a\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c34a5c637bc137e9f6d349bb15f7314a\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
82            </intent-filter>
83        </receiver>
84    </application>
85
86</manifest>
