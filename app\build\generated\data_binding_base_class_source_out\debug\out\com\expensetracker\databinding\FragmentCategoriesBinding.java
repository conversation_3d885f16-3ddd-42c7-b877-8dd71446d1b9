// Generated by view binder compiler. Do not edit!
package com.expensetracker.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.expensetracker.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentCategoriesBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final MaterialButton buttonAddFirstCategory;

  @NonNull
  public final FloatingActionButton fabAddCategory;

  @NonNull
  public final LinearLayout layoutEmptyState;

  @NonNull
  public final RecyclerView recyclerCategories;

  private FragmentCategoriesBinding(@NonNull CoordinatorLayout rootView,
      @NonNull MaterialButton buttonAddFirstCategory, @NonNull FloatingActionButton fabAddCategory,
      @NonNull LinearLayout layoutEmptyState, @NonNull RecyclerView recyclerCategories) {
    this.rootView = rootView;
    this.buttonAddFirstCategory = buttonAddFirstCategory;
    this.fabAddCategory = fabAddCategory;
    this.layoutEmptyState = layoutEmptyState;
    this.recyclerCategories = recyclerCategories;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentCategoriesBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentCategoriesBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_categories, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentCategoriesBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_add_first_category;
      MaterialButton buttonAddFirstCategory = ViewBindings.findChildViewById(rootView, id);
      if (buttonAddFirstCategory == null) {
        break missingId;
      }

      id = R.id.fab_add_category;
      FloatingActionButton fabAddCategory = ViewBindings.findChildViewById(rootView, id);
      if (fabAddCategory == null) {
        break missingId;
      }

      id = R.id.layout_empty_state;
      LinearLayout layoutEmptyState = ViewBindings.findChildViewById(rootView, id);
      if (layoutEmptyState == null) {
        break missingId;
      }

      id = R.id.recycler_categories;
      RecyclerView recyclerCategories = ViewBindings.findChildViewById(rootView, id);
      if (recyclerCategories == null) {
        break missingId;
      }

      return new FragmentCategoriesBinding((CoordinatorLayout) rootView, buttonAddFirstCategory,
          fabAddCategory, layoutEmptyState, recyclerCategories);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
