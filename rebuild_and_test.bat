@echo off
echo 🔧 重建並測試新增記帳功能
echo =====================================

echo.
echo 📋 第1步：重新建置專案...
gradlew.bat clean assembleDebug
if %errorlevel% neq 0 (
    echo ❌ 建置失敗
    pause
    exit /b 1
)
echo ✅ 建置成功

echo.
echo 📋 第2步：檢查模擬器狀態...
adb devices

echo.
echo 📋 第3步：安裝更新的APK...
adb install -r app\build\outputs\apk\debug\app-debug.apk
if %errorlevel% neq 0 (
    echo ❌ 安裝失敗
    pause
    exit /b 1
)
echo ✅ 安裝成功

echo.
echo 📋 第4步：啟動應用程式...
adb shell am start -n com.expensetracker/.MainActivity

echo.
echo 📋 第5步：查看除錯日誌...
echo 💡 現在請在模擬器中測試新增按鈕，同時觀察以下日誌：
echo.
adb logcat -s ExpensesFragment:D

pause