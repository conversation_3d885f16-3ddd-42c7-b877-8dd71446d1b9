package com.expensetracker.ui.adapter

import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.expensetracker.data.dao.CategoryWithStats
import com.expensetracker.databinding.ItemCategoryBinding

class CategoryAdapter(
    private val onCategoryClick: (CategoryWithStats) -> Unit,
    private val onMoreOptionsClick: (CategoryWithStats) -> Unit
) : ListAdapter<CategoryWithStats, CategoryAdapter.CategoryViewHolder>(CategoryDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CategoryViewHolder {
        val binding = ItemCategoryBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return CategoryViewHolder(binding)
    }

    override fun onBindViewHolder(holder: CategoryViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class CategoryViewHolder(
        private val binding: ItemCategoryBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(categoryWithStats: CategoryWithStats) {
            val category = categoryWithStats.category
            val expenseCount = categoryWithStats.expenseCount
            val totalAmount = categoryWithStats.totalAmount

            // 設置類別名稱
            binding.textCategoryName.text = category.name

            // 設置費用數量
            val expenseText = if (expenseCount == 1) {
                "$expenseCount 筆費用"
            } else {
                "$expenseCount 筆費用"
            }
            binding.textExpenseCount.text = expenseText

            // 設置總金額
            binding.textTotalAmount.text = String.format("$%.2f", totalAmount)

            // 設置類別顏色
            try {
                val categoryColor = Color.parseColor(category.color)
                binding.viewCategoryColor.backgroundTintList = 
                    android.content.res.ColorStateList.valueOf(categoryColor)
            } catch (e: IllegalArgumentException) {
                // 如果顏色格式錯誤，使用預設顏色
                binding.viewCategoryColor.backgroundTintList = 
                    android.content.res.ColorStateList.valueOf(Color.parseColor("#4CAF50"))
            }

            // 設置點擊事件
            binding.root.setOnClickListener {
                onCategoryClick(categoryWithStats)
            }

            binding.buttonMoreOptions.setOnClickListener {
                onMoreOptionsClick(categoryWithStats)
            }
        }
    }

    private class CategoryDiffCallback : DiffUtil.ItemCallback<CategoryWithStats>() {
        override fun areItemsTheSame(
            oldItem: CategoryWithStats,
            newItem: CategoryWithStats
        ): Boolean {
            return oldItem.category.id == newItem.category.id
        }

        override fun areContentsTheSame(
            oldItem: CategoryWithStats,
            newItem: CategoryWithStats
        ): Boolean {
            return oldItem.category == newItem.category &&
                    oldItem.expenseCount == newItem.expenseCount &&
                    oldItem.totalAmount == newItem.totalAmount
        }
    }
}