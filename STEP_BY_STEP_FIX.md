# 🔧 段階式修復指南

## 🎯 目標
逐步解決Kotlin/Room版本相容性問題，確保專案可以成功建置

## 📋 修復步驟

### 階段1：基本建置測試 ✅
**目標：** 確保基本Android專案可以建置

1. **執行完全清理**
   ```bash
   complete_clean.bat
   ```

2. **測試簡化版本建置**
   - 使用 `build_simple.gradle.kts`（無Room/Hilt）
   - 確認基本Android專案可以建置

3. **預期結果**
   - ✅ 建置成功
   - ✅ APK生成
   - ✅ 基本UI可以顯示

### 階段2：加回Navigation ⏳
**目標：** 確保Navigation Component正常運作

1. **檢查Navigation相關檔案**
   - `mobile_navigation.xml`
   - Fragment類別（移除Hilt註解）

2. **測試建置**
   ```bash
   gradlew clean assembleDebug
   ```

3. **預期結果**
   - ✅ 建置成功
   - ✅ 底部導航正常
   - ✅ Fragment切換正常

### 階段3：加回Room Database ⏳
**目標：** 解決Room版本相容性問題

1. **使用相容版本**
   ```kotlin
   // 使用較舊但穩定的版本
   implementation("androidx.room:room-runtime:2.5.0")
   implementation("androidx.room:room-ktx:2.5.0")
   ksp("androidx.room:room-compiler:2.5.0")
   ```

2. **或者降級Kotlin版本**
   ```kotlin
   // 在根build.gradle.kts中
   id("org.jetbrains.kotlin.android") version "1.8.22" apply false
   ```

3. **測試建置**
   ```bash
   gradlew clean assembleDebug
   ```

### 階段4：加回Hilt DI ⏳
**目標：** 確保依賴注入正常運作

1. **加回Hilt依賴**
   ```kotlin
   implementation("com.google.dagger:hilt-android:2.48")
   ksp("com.google.dagger:hilt-compiler:2.48")
   ```

2. **加回Hilt註解**
   - `@AndroidEntryPoint`
   - `@HiltViewModel`
   - `@Inject`

3. **測試建置**
   ```bash
   gradlew clean assembleDebug
   ```

### 階段5：完整功能測試 ⏳
**目標：** 確保所有功能正常運作

1. **測試新增記帳功能**
2. **測試資料庫操作**
3. **測試UI互動**

## 🔍 版本相容性矩陣

### 選項1：降級Kotlin（推薦）
```kotlin
// 根 build.gradle.kts
kotlin = "1.8.22"
room = "2.6.1"
hilt = "2.48"
```

### 選項2：升級Room
```kotlin
// app build.gradle.kts
kotlin = "1.9.10"
room = "2.7.0-alpha12"  // 支援新Kotlin metadata
hilt = "2.48"
```

### 選項3：使用KSP（已實作）
```kotlin
// 已經在使用，但仍有相容性問題
plugins {
    id("com.google.devtools.ksp")
}
```

## 🚨 常見問題解決

### 問題1：仍然執行KAPT任務
**解決方案：**
1. 完全清理快取
2. 確認沒有任何`kapt`引用
3. 重新建置

### 問題2：Metadata版本不相容
**解決方案：**
1. 降級Kotlin到1.8.22
2. 或升級Room到alpha版本
3. 確保版本一致性

### 問題3：Hilt編譯錯誤
**解決方案：**
1. 先移除Hilt測試基本功能
2. 確認Room正常後再加回Hilt
3. 檢查註解處理器配置

## 📊 測試檢查清單

### 階段1測試
- [ ] ✅ 基本專案建置成功
- [ ] ✅ APK生成
- [ ] ✅ 應用程式啟動
- [ ] ✅ 主畫面顯示

### 階段2測試
- [ ] ✅ 底部導航顯示
- [ ] ✅ Fragment切換正常
- [ ] ✅ 無崩潰錯誤

### 階段3測試
- [ ] ✅ Room編譯成功
- [ ] ✅ 資料庫建立正常
- [ ] ✅ DAO查詢正常

### 階段4測試
- [ ] ✅ Hilt注入成功
- [ ] ✅ ViewModel建立正常
- [ ] ✅ Repository注入正常

### 階段5測試
- [ ] ✅ 新增記帳功能正常
- [ ] ✅ 表單驗證正常
- [ ] ✅ 資料儲存正常

## 💡 建議

1. **一次只修復一個問題**
2. **每個階段都要測試建置**
3. **保留備份檔案**
4. **記錄成功的配置**

---

**📝 當前狀態：** 階段1 - 基本建置測試
**📝 下一步：** 執行 `complete_clean.bat` 並測試簡化版本