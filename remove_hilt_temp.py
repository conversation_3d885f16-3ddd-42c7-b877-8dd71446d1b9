#!/usr/bin/env python3
"""
暫時移除所有Hilt相關程式碼來測試基本功能
"""
import os
import re

def remove_hilt_from_file(file_path):
    """移除檔案中的Hilt相關程式碼"""
    if not os.path.exists(file_path):
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 移除Hilt import
        content = re.sub(r'import dagger\.hilt\..*\n', '', content)
        
        # 移除@AndroidEntryPoint註解
        content = re.sub(r'@AndroidEntryPoint\n', '', content)
        
        # 移除@HiltViewModel註解
        content = re.sub(r'@HiltViewModel\n', '', content)
        
        # 移除@Inject註解
        content = re.sub(r'@Inject\n', '', content)
        
        # 移除viewModels()調用，替換為簡單版本
        content = re.sub(r'private val (\w+): (\w+) by viewModels\(\)', 
                        r'// private val \1: \2 by viewModels() // 暫時移除', content)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 已處理 {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ 處理 {file_path} 時發生錯誤: {e}")
        return False

def main():
    """主要處理流程"""
    print("🔧 暫時移除Hilt相關程式碼")
    print("="*50)
    
    # 需要處理的檔案
    files_to_process = [
        "app/src/main/java/com/expensetracker/ui/categories/CategoriesFragment.kt",
        "app/src/main/java/com/expensetracker/ui/reports/ReportsFragment.kt",
        "app/src/main/java/com/expensetracker/ExpenseTrackerApplication.kt"
    ]
    
    for file_path in files_to_process:
        remove_hilt_from_file(file_path)
    
    print("\n✅ 處理完成！")
    print("💡 現在可以嘗試建置專案")

if __name__ == "__main__":
    main()