{"logs": [{"outputFile": "com.expensetracker.app-mergeDebugResources-47:/values-gl/values-gl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\26d210e50acf91f7815d7153f1eac7d8\\transformed\\core-1.12.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,783", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "149,251,351,449,556,662,778,879"}, "to": {"startLines": "38,39,40,41,42,43,44,111", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3519,3618,3720,3820,3918,4025,4131,9837", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "3613,3715,3815,3913,4020,4126,4242,9933"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\eff9940f5c7059a17075146307404186\\transformed\\navigation-ui-2.7.6\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,173", "endColumns": "117,121", "endOffsets": "168,290"}, "to": {"startLines": "107,108", "startColumns": "4,4", "startOffsets": "9429,9547", "endColumns": "117,121", "endOffsets": "9542,9664"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7672088a0399f169025805b796070182\\transformed\\material-1.11.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,275,356,436,521,623,719,824,957,1037,1115,1211,1290,1353,1448,1512,1581,1644,1718,1782,1838,1959,2017,2079,2135,2212,2351,2439,2519,2659,2739,2819,2968,3058,3139,3195,3251,3317,3396,3477,3565,3653,3732,3809,3891,3980,4081,4165,4257,4350,4451,4525,4617,4719,4771,4855,4921,5013,5101,5163,5227,5290,5360,5471,5576,5682,5781,5841,5901", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,79,84,101,95,104,132,79,77,95,78,62,94,63,68,62,73,63,55,120,57,61,55,76,138,87,79,139,79,79,148,89,80,55,55,65,78,80,87,87,78,76,81,88,100,83,91,92,100,73,91,101,51,83,65,91,87,61,63,62,69,110,104,105,98,59,59,84", "endOffsets": "270,351,431,516,618,714,819,952,1032,1110,1206,1285,1348,1443,1507,1576,1639,1713,1777,1833,1954,2012,2074,2130,2207,2346,2434,2514,2654,2734,2814,2963,3053,3134,3190,3246,3312,3391,3472,3560,3648,3727,3804,3886,3975,4076,4160,4252,4345,4446,4520,4612,4714,4766,4850,4916,5008,5096,5158,5222,5285,5355,5466,5571,5677,5776,5836,5896,5981"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3075,3156,3236,3321,3423,4247,4352,4485,4565,4643,4739,4818,4881,4976,5040,5109,5172,5246,5310,5366,5487,5545,5607,5663,5740,5879,5967,6047,6187,6267,6347,6496,6586,6667,6723,6779,6845,6924,7005,7093,7181,7260,7337,7419,7508,7609,7693,7785,7878,7979,8053,8145,8247,8299,8383,8449,8541,8629,8691,8755,8818,8888,8999,9104,9210,9309,9369,9669", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,109", "endColumns": "12,80,79,84,101,95,104,132,79,77,95,78,62,94,63,68,62,73,63,55,120,57,61,55,76,138,87,79,139,79,79,148,89,80,55,55,65,78,80,87,87,78,76,81,88,100,83,91,92,100,73,91,101,51,83,65,91,87,61,63,62,69,110,104,105,98,59,59,84", "endOffsets": "320,3151,3231,3316,3418,3514,4347,4480,4560,4638,4734,4813,4876,4971,5035,5104,5167,5241,5305,5361,5482,5540,5602,5658,5735,5874,5962,6042,6182,6262,6342,6491,6581,6662,6718,6774,6840,6919,7000,7088,7176,7255,7332,7414,7503,7604,7688,7780,7873,7974,8048,8140,8242,8294,8378,8444,8536,8624,8686,8750,8813,8883,8994,9099,9205,9304,9364,9424,9749"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7ed8444f8e1a364776fa1564eb273e2d\\transformed\\appcompat-1.6.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,313,421,506,607,735,821,902,994,1088,1185,1279,1379,1473,1569,1664,1756,1848,1929,2037,2144,2251,2360,2465,2579,2756,2855", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "204,308,416,501,602,730,816,897,989,1083,1180,1274,1374,1468,1564,1659,1751,1843,1924,2032,2139,2246,2355,2460,2574,2751,2850,2933"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "325,429,533,641,726,827,955,1041,1122,1214,1308,1405,1499,1599,1693,1789,1884,1976,2068,2149,2257,2364,2471,2580,2685,2799,2976,9754", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "424,528,636,721,822,950,1036,1117,1209,1303,1400,1494,1594,1688,1784,1879,1971,2063,2144,2252,2359,2466,2575,2680,2794,2971,3070,9832"}}]}]}