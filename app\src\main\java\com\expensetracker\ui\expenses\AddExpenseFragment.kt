package com.expensetracker.ui.expenses

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.expensetracker.R
import com.expensetracker.databinding.FragmentAddExpenseBinding

// 暫時移除Hilt來測試基本功能
class AddExpenseFragment : Fragment() {

    private var _binding: FragmentAddExpenseBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        Log.d("AddExpenseFragment", "onCreateView called")
        _binding = FragmentAddExpenseBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Log.d("AddExpenseFragment", "onViewCreated called")
        
        setupUI()
    }

    private fun setupUI() {
        Log.d("AddExpenseFragment", "setupUI called")
        
        // 暫時只設置基本按鈕功能
        binding.buttonCancel.setOnClickListener {
            Log.d("AddExpenseFragment", "Cancel button clicked")
            Toast.makeText(requireContext(), "Cancel clicked!", Toast.LENGTH_SHORT).show()
            findNavController().navigateUp()
        }
        
        binding.buttonSave.setOnClickListener {
            Log.d("AddExpenseFragment", "Save button clicked")
            Toast.makeText(requireContext(), "Save clicked! (功能開發中)", Toast.LENGTH_SHORT).show()
            // 暫時直接返回
            findNavController().navigateUp()
        }
        
        // 顯示成功載入訊息
        Toast.makeText(requireContext(), "新增記帳頁面載入成功！", Toast.LENGTH_SHORT).show()
    }

    override fun onDestroyView() {
        Log.d("AddExpenseFragment", "onDestroyView called")
        super.onDestroyView()
        _binding = null
    }
}