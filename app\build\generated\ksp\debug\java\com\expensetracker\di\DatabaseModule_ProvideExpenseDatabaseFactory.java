package com.expensetracker.di;

import android.content.Context;
import com.expensetracker.data.database.ExpenseDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideExpenseDatabaseFactory implements Factory<ExpenseDatabase> {
  private final Provider<Context> contextProvider;

  public DatabaseModule_ProvideExpenseDatabaseFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public ExpenseDatabase get() {
    return provideExpenseDatabase(contextProvider.get());
  }

  public static DatabaseModule_ProvideExpenseDatabaseFactory create(
      Provider<Context> contextProvider) {
    return new DatabaseModule_ProvideExpenseDatabaseFactory(contextProvider);
  }

  public static ExpenseDatabase provideExpenseDatabase(Context context) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideExpenseDatabase(context));
  }
}
