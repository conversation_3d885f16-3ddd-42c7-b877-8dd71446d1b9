<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_home" modulePackage="com.expensetracker" filePath="app\src\main\res\layout\fragment_home.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_home_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="222" endOffset="12"/></Target><Target id="@+id/text_total_expenses" view="TextView"><Expressions/><location startLine="75" startOffset="20" endLine="82" endOffset="64"/></Target><Target id="@+id/text_monthly_expenses" view="TextView"><Expressions/><location startLine="110" startOffset="20" endLine="117" endOffset="66"/></Target><Target id="@+id/button_add_expense" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="152" startOffset="20" endLine="159" endOffset="56"/></Target><Target id="@+id/button_view_all_expenses" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="161" startOffset="20" endLine="168" endOffset="57"/></Target><Target id="@+id/recycler_recent_expenses" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="196" startOffset="16" endLine="203" endOffset="59"/></Target><Target id="@+id/text_no_expenses" view="TextView"><Expressions/><location startLine="205" startOffset="16" endLine="214" endOffset="50"/></Target></Targets></Layout>