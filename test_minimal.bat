@echo off
echo 🧪 測試最小化版本
echo =====================================

echo.
echo 📋 第1步：完全清理...
if exist ".gradle" rmdir /s /q .gradle
if exist "app\build" rmdir /s /q app\build
if exist "build" rmdir /s /q build

echo.
echo 📋 第2步：嘗試編譯Kotlin檔案...
gradlew.bat clean
gradlew.bat compileDebugKotlin

if %errorlevel% neq 0 (
    echo ❌ Kotlin編譯失敗
    echo 💡 請檢查錯誤訊息
    pause
    exit /b 1
)

echo ✅ Kotlin編譯成功！

echo.
echo 📋 第3步：完整建置...
gradlew.bat assembleDebug

if %errorlevel% neq 0 (
    echo ❌ 完整建置失敗
    pause
    exit /b 1
)

echo ✅ 建置成功！

echo.
echo 📋 第4步：安裝測試...
adb install -r app\build\outputs\apk\debug\app-debug.apk
adb shell am start -n com.expensetracker/.MainActivity

echo.
echo 🎉 測試完成！請在模擬器中測試基本功能。

pause