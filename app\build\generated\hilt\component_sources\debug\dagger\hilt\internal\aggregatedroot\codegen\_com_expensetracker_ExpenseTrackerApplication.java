package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.expensetracker.ExpenseTrackerApplication",
    rootPackage = "com.expensetracker",
    originatingRoot = "com.expensetracker.ExpenseTrackerApplication",
    originatingRootPackage = "com.expensetracker",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "ExpenseTrackerApplication",
    originatingRootSimpleNames = "ExpenseTrackerApplication"
)
public class _com_expensetracker_ExpenseTrackerApplication {
}
