# 🔧 建置問題修復指南

## 🎯 問題描述
遇到 Kotlin metadata 版本相容性錯誤：
```
Provided Metadata instance has version 2.1.0, while maximum supported version is 2.0.0
```

## ✅ 已實施的修復方案

### 1. 🔄 版本相容性修復
- **降級 Kotlin 版本**：從 1.9.25 → 1.9.10
- **使用 KSP 替代 KAPT**：更好的相容性和效能
- **保持 Room 版本**：2.6.1 (穩定版本)

### 2. 📝 修改的檔案

#### `build.gradle.kts` (專案級別)
```kotlin
plugins {
    id("com.android.application") version "8.11.0" apply false
    id("org.jetbrains.kotlin.android") version "1.9.10" apply false  // 降級
    id("com.google.devtools.ksp") version "1.9.10-1.0.13" apply false  // 新增 KSP
    id("com.google.dagger.hilt.android") version "2.48" apply false
}
```

#### `app/build.gradle.kts` (應用程式級別)
```kotlin
plugins {
    id("com.android.application")
    id("org.jetbrains.kotlin.android")
    id("com.google.devtools.ksp")  // 替代 kotlin-kapt
    id("kotlin-parcelize")
    id("dagger.hilt.android.plugin")
}

dependencies {
    // Room Database - 使用 KSP
    implementation("androidx.room:room-runtime:2.6.1")
    implementation("androidx.room:room-ktx:2.6.1")
    ksp("androidx.room:room-compiler:2.6.1")  // 改為 ksp
    
    // Hilt Dependency Injection - 使用 KSP
    implementation("com.google.dagger:hilt-android:2.48")
    ksp("com.google.dagger:hilt-compiler:2.48")  // 改為 ksp
}
```

## 🚀 建置步驟

### 方法1：使用批次檔（推薦）
```bash
# 清理並重建
clean_and_rebuild.bat

# 診斷問題（如果仍有錯誤）
python diagnose_build_issues.py
```

### 方法2：使用 Gradle 命令
```bash
# 清理專案
./gradlew clean

# 重新建置
./gradlew assembleDebug
```

### 方法3：使用 Android Studio
1. **開啟專案**：File > Open > 選擇 BookKeeping2025 資料夾
2. **同步專案**：等待 Gradle 同步完成
3. **清理專案**：Build > Clean Project
4. **重建專案**：Build > Rebuild Project

## 🔍 故障排除

### 問題1：Gradle Wrapper 缺失
**症狀：** `gradlew.bat` 無法執行
**解決方案：**
```bash
# 在有 Gradle 的環境中執行
gradle wrapper --gradle-version 8.9
```

### 問題2：依賴下載失敗
**症狀：** 網路連線錯誤
**解決方案：**
1. 檢查網路連線
2. 配置代理設定（如果需要）
3. 使用 Android Studio 的內建下載

### 問題3：Android SDK 版本問題
**症狀：** SDK 版本不匹配
**解決方案：**
1. 開啟 Android Studio
2. Tools > SDK Manager
3. 安裝 Android API 35
4. 更新 Build Tools

### 問題4：Java 版本相容性
**症狀：** Java 版本錯誤
**解決方案：**
1. 確認使用 Java 8 或更高版本
2. 在 Android Studio 中設定正確的 JDK 路徑

## 📊 版本相容性矩陣

| 元件 | 版本 | 狀態 |
|------|------|------|
| Kotlin | 1.9.10 | ✅ 穩定 |
| Android Gradle Plugin | 8.11.0 | ✅ 穩定 |
| Room | 2.6.1 | ✅ 穩定 |
| Hilt | 2.48 | ✅ 穩定 |
| KSP | 1.9.10-1.0.13 | ✅ 推薦 |
| Compile SDK | 35 | ✅ 最新 |
| Target SDK | 35 | ✅ 最新 |
| Min SDK | 33 | ✅ 現代 |

## 🎯 預期結果

### 成功建置後應該看到：
```
BUILD SUCCESSFUL in Xs
```

### 生成的檔案：
- `app/build/outputs/apk/debug/app-debug.apk`

## 🧪 測試建置是否成功

### 1. 檢查 APK 檔案
```bash
# 檢查檔案是否存在
ls app/build/outputs/apk/debug/app-debug.apk
```

### 2. 安裝到模擬器
```bash
# 安裝 APK
adb install -r app/build/outputs/apk/debug/app-debug.apk

# 啟動應用程式
adb shell am start -n com.expensetracker/.MainActivity
```

### 3. 驗證功能
- 應用程式正常啟動
- 可以導航到不同分頁
- 新增記帳功能可用

## 💡 最佳實踐建議

### 1. 版本管理
- 使用穩定版本的依賴
- 定期更新但測試相容性
- 記錄版本變更

### 2. 建置優化
- 使用 KSP 替代 KAPT
- 啟用 Gradle 快取
- 使用並行建置

### 3. 問題預防
- 定期清理建置快取
- 保持 Android Studio 更新
- 監控依賴安全性

## 🆘 如果仍有問題

### 1. 收集資訊
- 完整的錯誤日誌
- Gradle 版本資訊
- Android Studio 版本
- 作業系統資訊

### 2. 嘗試替代方案
- 使用不同的 Kotlin 版本
- 嘗試 KAPT 和 KSP 的組合
- 降級到更穩定的依賴版本

### 3. 社群支援
- 查看 GitHub Issues
- Stack Overflow 搜尋
- Android 開發者社群

---

**📝 最後更新：** 2025年1月15日  
**🎯 適用版本：** BookKeeping2025 v1.0