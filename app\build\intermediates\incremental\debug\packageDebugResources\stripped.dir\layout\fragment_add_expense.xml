<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".ui.expenses.AddExpenseFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Card -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/add_expense"
                    android:textAppearance="@style/TextAppearance.Material3.HeadlineMedium"
                    android:textColor="?attr/colorPrimary" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/add_expense_subtitle"
                    android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                    android:textColor="?attr/colorOnSurfaceVariant" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Form Card -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <!-- Amount Input -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_amount"
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:hint="@string/expense_amount"
                    app:errorEnabled="true"
                    app:prefixText="$"
                    app:startIconDrawable="@drawable/ic_attach_money_24">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edit_amount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="numberDecimal"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Description Input -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_description"
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:hint="@string/expense_description"
                    app:counterEnabled="true"
                    app:counterMaxLength="100"
                    app:errorEnabled="true"
                    app:startIconDrawable="@drawable/ic_description_24">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edit_description"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textCapSentences"
                        android:maxLength="100"
                        android:maxLines="2" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Category Selection -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_category"
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:hint="@string/expense_category"
                    app:errorEnabled="true"
                    app:startIconDrawable="@drawable/ic_category_24">

                    <AutoCompleteTextView
                        android:id="@+id/dropdown_category"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Date Selection -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_date"
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:hint="@string/expense_date"
                    app:errorEnabled="true"
                    app:startIconDrawable="@drawable/ic_calendar_24">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edit_date"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:clickable="true"
                        android:focusable="false"
                        android:inputType="none"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Payment Method Selection -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_payment_method"
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:hint="@string/payment_method"
                    app:startIconDrawable="@drawable/ic_payment_24">

                    <AutoCompleteTextView
                        android:id="@+id/dropdown_payment_method"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Optional Details Card -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="@string/optional_details"
                    android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                    android:textColor="?attr/colorPrimary" />

                <!-- Notes Input -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_notes"
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:hint="@string/expense_notes"
                    app:counterEnabled="true"
                    app:counterMaxLength="200"
                    app:startIconDrawable="@drawable/ic_note_24">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edit_notes"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textMultiLine"
                        android:maxLength="200"
                        android:maxLines="3"
                        android:minLines="2" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Location Input -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_location"
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/expense_location"
                    app:counterEnabled="true"
                    app:counterMaxLength="50"
                    app:startIconDrawable="@drawable/ic_location_24">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edit_location"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textCapWords"
                        android:maxLength="50"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/button_cancel"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:text="@string/cancel"
                app:icon="@drawable/ic_close_24" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/button_save"
                style="@style/Widget.Material3.Button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:text="@string/save"
                app:icon="@drawable/ic_save_24" />

        </LinearLayout>

        <!-- Bottom Spacing -->
        <View
            android:layout_width="match_parent"
            android:layout_height="16dp" />

    </LinearLayout>

</ScrollView>