package com.expensetracker.data.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.expensetracker.data.entity.Category

@Dao
interface CategoryDao {
    
    @Query("SELECT * FROM categories ORDER BY name ASC")
    fun getAllCategories(): LiveData<List<Category>>
    
    @Query("SELECT * FROM categories ORDER BY name ASC")
    suspend fun getAllCategoriesSync(): List<Category>
    
    @Query("SELECT * FROM categories WHERE id = :categoryId")
    suspend fun getCategoryById(categoryId: Long): Category?
    
    @Query("SELECT * FROM categories WHERE id = :categoryId")
    fun getCategoryByIdLiveData(categoryId: Long): LiveData<Category?>
    
    @Query("SELECT * FROM categories WHERE name = :name LIMIT 1")
    suspend fun getCategoryByName(name: String): Category?
    
    @Query("SELECT * FROM categories WHERE is_default = 1 ORDER BY name ASC")
    suspend fun getDefaultCategories(): List<Category>
    
    @Query("SELECT * FROM categories WHERE is_default = 0 ORDER BY name ASC")
    suspend fun getCustomCategories(): List<Category>
    
    @Query("SELECT COUNT(*) FROM categories")
    suspend fun getCategoryCount(): Int
    
    @Query("""
        SELECT c.*, COUNT(e.id) as expense_count, COALESCE(SUM(e.amount), 0) as total_amount
        FROM categories c
        LEFT JOIN expenses e ON c.id = e.category_id
        GROUP BY c.id
        ORDER BY c.name ASC
    """)
    fun getCategoriesWithStats(): LiveData<List<CategoryWithStats>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCategory(category: Category): Long
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCategories(categories: List<Category>): List<Long>
    
    @Update
    suspend fun updateCategory(category: Category)
    
    @Delete
    suspend fun deleteCategory(category: Category)
    
    @Query("DELETE FROM categories WHERE id = :categoryId")
    suspend fun deleteCategoryById(categoryId: Long)
    
    @Query("DELETE FROM categories WHERE is_default = 0")
    suspend fun deleteAllCustomCategories()
    
    @Query("UPDATE categories SET updated_at = :timestamp WHERE id = :categoryId")
    suspend fun updateCategoryTimestamp(categoryId: Long, timestamp: Long = System.currentTimeMillis())
}

// Data class for category statistics
data class CategoryWithStats(
    @Embedded val category: Category,
    @ColumnInfo(name = "expense_count") val expenseCount: Int,
    @ColumnInfo(name = "total_amount") val totalAmount: Double
)