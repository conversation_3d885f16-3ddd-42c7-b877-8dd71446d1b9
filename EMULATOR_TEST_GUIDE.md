# 📱 模擬器測試指南 - 新增記帳功能

## 🎯 測試目標
在Android模擬器中完整測試「新增一筆記帳」功能的所有面向

## 🚀 準備工作

### 1. 環境檢查
- [ ] ✅ Android Studio 已安裝
- [ ] ✅ Android SDK 已配置
- [ ] ✅ 模擬器 `Medium_Phone_API_36` 可用
- [ ] ✅ ADB 工具可用

### 2. 建置專案
```bash
# 方法1：使用批次檔
build_and_test.bat

# 方法2：使用Android Studio
# 1. 開啟 Android Studio
# 2. 選擇 "Open an existing project"
# 3. 選擇 BookKeeping2025 資料夾
# 4. 等待 Gradle 同步完成
# 5. 點擊 "Build" > "Make Project"
```

### 3. 啟動模擬器
```bash
# 方法1：使用Android Studio
# Device Manager > 啟動 Medium_Phone_API_36

# 方法2：使用命令列
emulator -avd Medium_Phone_API_36
```

## 📋 詳細測試步驟

### 🏠 **階段1：應用程式啟動測試**

#### 1.1 安裝應用程式
```bash
# 使用批次檔
test_on_emulator.bat

# 或手動安裝
adb install -r app/build/outputs/apk/debug/app-debug.apk
```

#### 1.2 啟動應用程式
```bash
adb shell am start -n com.expensetracker/.MainActivity
```

#### 1.3 檢查項目
- [ ] ✅ 應用程式成功啟動
- [ ] ✅ 主畫面正常顯示
- [ ] ✅ 底部導航欄顯示4個分頁
- [ ] ✅ 沒有崩潰或錯誤訊息

### 📊 **階段2：導航到新增記帳功能**

#### 2.1 點擊 Expenses 分頁
- [ ] ✅ 成功切換到 Expenses 分頁
- [ ] ✅ 顯示記帳列表（可能為空）
- [ ] ✅ 右下角顯示 FAB 新增按鈕

#### 2.2 點擊 FAB 新增按鈕
- [ ] ✅ 成功導航到新增記帳頁面
- [ ] ✅ 表單正確顯示所有欄位
- [ ] ✅ 頁面標題顯示 "Add Expense"

### 📝 **階段3：表單功能測試**

#### 3.1 金額欄位測試
**測試案例1：正常輸入**
- 輸入：`25.99`
- 預期：✅ 正常接受輸入

**測試案例2：空白驗證**
- 輸入：留空
- 點擊 Save
- 預期：❌ 顯示錯誤訊息 "Please enter an amount"

**測試案例3：無效金額**
- 輸入：`-10` 或 `abc`
- 點擊 Save
- 預期：❌ 顯示錯誤訊息 "Please enter a valid amount"

#### 3.2 描述欄位測試
**測試案例1：正常輸入**
- 輸入：`午餐 - 麥當勞`
- 預期：✅ 正常接受輸入

**測試案例2：空白驗證**
- 輸入：留空
- 點擊 Save
- 預期：❌ 顯示錯誤訊息 "Please enter a description"

**測試案例3：過短描述**
- 輸入：`a`
- 點擊 Save
- 預期：❌ 顯示錯誤訊息 "Description must be at least 2 characters"

#### 3.3 分類選擇測試
**測試案例1：選擇分類**
- 點擊分類下拉選單
- 預期：✅ 顯示可用分類列表
- 選擇一個分類
- 預期：✅ 分類正確選中

**測試案例2：未選擇分類**
- 不選擇任何分類
- 點擊 Save
- 預期：❌ 顯示錯誤訊息 "Please select a category"

#### 3.4 日期選擇測試
**測試案例1：使用日期選擇器**
- 點擊日期欄位
- 預期：✅ 顯示日期選擇器
- 選擇一個日期
- 預期：✅ 日期正確顯示在欄位中

**測試案例2：預設日期**
- 預期：✅ 預設顯示今天的日期

#### 3.5 付款方式測試
**測試案例1：選擇付款方式**
- 點擊付款方式下拉選單
- 預期：✅ 顯示7種付款方式
- 選擇一種付款方式
- 預期：✅ 付款方式正確選中

### 💾 **階段4：儲存功能測試**

#### 4.1 成功儲存測試
**完整表單填寫：**
- 金額：`25.99`
- 描述：`午餐 - 麥當勞`
- 分類：選擇 "Food & Dining"
- 日期：選擇今天
- 付款方式：選擇 "Cash"
- 備註：`很好吃`（選填）
- 地點：`台北市`（選填）

**點擊 Save 按鈕：**
- [ ] ✅ 顯示載入狀態（按鈕文字變為 "Saving..."）
- [ ] ✅ 顯示成功訊息 "Expense created successfully"
- [ ] ✅ 自動返回記帳列表頁面
- [ ] ✅ 新增的記錄出現在列表中

#### 4.2 取消功能測試
- 填寫部分表單
- 點擊 Cancel 按鈕
- 預期：✅ 返回記帳列表頁面，不儲存資料

### 🔄 **階段5：重複測試**

#### 5.1 多筆記帳測試
- 重複新增3-5筆不同的記帳記錄
- 使用不同的分類和付款方式
- 確認每筆記錄都正確儲存和顯示

#### 5.2 邊界值測試
- 測試極大金額：`999999.99`
- 測試極小金額：`0.01`
- 測試長描述：100字元的描述
- 測試特殊字元：包含符號的描述

## 📊 測試結果記錄

### ✅ 成功的功能
- [ ] 應用程式啟動
- [ ] 頁面導航
- [ ] 表單顯示
- [ ] 金額驗證
- [ ] 描述驗證
- [ ] 分類選擇
- [ ] 日期選擇
- [ ] 付款方式選擇
- [ ] 資料儲存
- [ ] 成功回饋
- [ ] 列表更新

### ❌ 發現的問題
1. **問題描述：** [記錄發現的任何問題]
   - **重現步驟：** [如何重現問題]
   - **預期行為：** [應該如何運作]
   - **實際行為：** [實際發生什麼]

2. **問題描述：** [記錄發現的任何問題]
   - **重現步驟：** [如何重現問題]
   - **預期行為：** [應該如何運作]
   - **實際行為：** [實際發生什麼]

### 🎯 效能評估
- [ ] ✅ 頁面載入速度快
- [ ] ✅ 表單回應靈敏
- [ ] ✅ 資料儲存速度快
- [ ] ✅ 記憶體使用正常

### 🎨 UI/UX 評估
- [ ] ✅ 介面美觀
- [ ] ✅ 操作直觀
- [ ] ✅ 錯誤訊息清楚
- [ ] ✅ 載入狀態明確

## 🔧 故障排除

### 常見問題解決方案

#### 問題1：應用程式無法安裝
**解決方案：**
```bash
# 卸載舊版本
adb uninstall com.expensetracker

# 重新安裝
adb install app/build/outputs/apk/debug/app-debug.apk
```

#### 問題2：應用程式崩潰
**解決方案：**
```bash
# 查看崩潰日誌
adb logcat | grep com.expensetracker
```

#### 問題3：資料庫錯誤
**解決方案：**
```bash
# 清除應用程式資料
adb shell pm clear com.expensetracker
```

## 📝 測試完成檢查清單

### 基本功能
- [ ] ✅ 所有表單欄位正常運作
- [ ] ✅ 所有驗證規則正確執行
- [ ] ✅ 資料正確儲存到資料庫
- [ ] ✅ UI回饋機制正常

### 進階功能
- [ ] ✅ 錯誤處理機制完善
- [ ] ✅ 使用者體驗流暢
- [ ] ✅ 效能表現良好
- [ ] ✅ 沒有記憶體洩漏

### 整體評估
- [ ] ✅ 功能完整性：100%
- [ ] ✅ 穩定性：無崩潰
- [ ] ✅ 使用者體驗：優秀
- [ ] ✅ 準備發布：是/否

---

**📌 測試完成後請更新此文件，記錄所有發現的問題和建議改進的地方。**