#!/usr/bin/env python3
"""
檢查重複的類別宣告
"""
import os
import re

def find_kotlin_files():
    """找到所有Kotlin檔案"""
    kotlin_files = []
    for root, dirs, files in os.walk("app/src/main/java"):
        for file in files:
            if file.endswith(".kt"):
                kotlin_files.append(os.path.join(root, file))
    return kotlin_files

def check_class_declarations(kotlin_files):
    """檢查類別宣告"""
    class_declarations = {}
    
    for file_path in kotlin_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 找到類別宣告
            class_matches = re.findall(r'class\s+(\w+)', content)
            for class_name in class_matches:
                if class_name not in class_declarations:
                    class_declarations[class_name] = []
                class_declarations[class_name].append(file_path)
        
        except Exception as e:
            print(f"❌ 讀取 {file_path} 時發生錯誤: {e}")
    
    return class_declarations

def main():
    """主要檢查流程"""
    print("🔍 檢查重複的類別宣告")
    print("="*50)
    
    kotlin_files = find_kotlin_files()
    print(f"📁 找到 {len(kotlin_files)} 個Kotlin檔案")
    
    class_declarations = check_class_declarations(kotlin_files)
    
    # 檢查重複
    duplicates_found = False
    for class_name, file_list in class_declarations.items():
        if len(file_list) > 1:
            print(f"\n❌ 重複的類別: {class_name}")
            for file_path in file_list:
                print(f"   📄 {file_path}")
            duplicates_found = True
    
    if not duplicates_found:
        print("\n✅ 沒有找到重複的類別宣告")
    
    # 特別檢查MainActivity
    if "MainActivity" in class_declarations:
        print(f"\n📱 MainActivity 宣告:")
        for file_path in class_declarations["MainActivity"]:
            print(f"   📄 {file_path}")

if __name__ == "__main__":
    main()