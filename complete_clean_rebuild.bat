@echo off
echo 🧹 完全清理並重建
echo =====================================

echo.
echo 📋 第1步：停止所有Gradle程序...
taskkill /f /im gradle.exe 2>nul
taskkill /f /im java.exe /fi "WINDOWTITLE eq Gradle*" 2>nul

echo.
echo 📋 第2步：刪除所有建置檔案...
if exist ".gradle" (
    echo 🗑️  刪除 .gradle...
    rmdir /s /q .gradle
)
if exist "app\build" (
    echo 🗑️  刪除 app\build...
    rmdir /s /q app\build
)
if exist "build" (
    echo 🗑️  刪除 build...
    rmdir /s /q build
)

echo.
echo 📋 第3步：清理全域Gradle快取...
if exist "%USERPROFILE%\.gradle\caches" (
    echo 🗑️  清理全域快取...
    rmdir /s /q "%USERPROFILE%\.gradle\caches"
)

echo.
echo 📋 第4步：檢查檔案結構...
echo 📁 檢查MainActivity...
if exist "app\src\main\java\com\expensetracker\MainActivity.kt" (
    echo ✅ MainActivity.kt 存在
) else (
    echo ❌ MainActivity.kt 不存在
)

echo.
echo 📋 第5步：重新建置...
gradlew.bat clean
if %errorlevel% neq 0 (
    echo ❌ Clean失敗
    pause
    exit /b 1
)

echo ✅ Clean成功

gradlew.bat compileDebugKotlin
if %errorlevel% neq 0 (
    echo ❌ Kotlin編譯失敗
    pause
    exit /b 1
)

echo ✅ Kotlin編譯成功

gradlew.bat assembleDebug
if %errorlevel% neq 0 (
    echo ❌ 完整建置失敗
    pause
    exit /b 1
)

echo ✅ 建置成功！

echo.
echo 📋 第6步：安裝並測試...
adb install -r app\build\outputs\apk\debug\app-debug.apk
if %errorlevel% equ 0 (
    echo ✅ 安裝成功
    adb shell am start -n com.expensetracker/.MainActivity
    echo 🎉 應用程式已啟動！
) else (
    echo ❌ 安裝失敗
)

pause