package com.expensetracker.data.entity;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\t\u0010\u000b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\f\u001a\u00020\u0005H\u00c6\u0003J\u001d\u0010\r\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u00c6\u0001J\t\u0010\u000e\u001a\u00020\u000fH\u00d6\u0001J\u0013\u0010\u0010\u001a\u00020\u00112\b\u0010\u0012\u001a\u0004\u0018\u00010\u0013H\u00d6\u0003J\u0006\u0010\u0014\u001a\u00020\u000fJ\u0006\u0010\u0015\u001a\u00020\u0016J\u0010\u0010\u0017\u001a\u00020\u00162\b\b\u0002\u0010\u0018\u001a\u00020\u0016J\t\u0010\u0019\u001a\u00020\u000fH\u00d6\u0001J\t\u0010\u001a\u001a\u00020\u0016H\u00d6\u0001J\u0019\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020\u000fH\u00d6\u0001R\u0016\u0010\u0004\u001a\u00020\u00058\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\n\u00a8\u0006 "}, d2 = {"Lcom/expensetracker/data/entity/ExpenseWithCategory;", "Landroid/os/Parcelable;", "expense", "Lcom/expensetracker/data/entity/Expense;", "category", "Lcom/expensetracker/data/entity/Category;", "(Lcom/expensetracker/data/entity/Expense;Lcom/expensetracker/data/entity/Category;)V", "getCategory", "()Lcom/expensetracker/data/entity/Category;", "getExpense", "()Lcom/expensetracker/data/entity/Expense;", "component1", "component2", "copy", "describeContents", "", "equals", "", "other", "", "getCategoryColorInt", "getDisplayText", "", "getFormattedAmountWithCurrency", "currencySymbol", "hashCode", "toString", "writeToParcel", "", "parcel", "Landroid/os/Parcel;", "flags", "app_debug"})
@kotlinx.parcelize.Parcelize()
public final class ExpenseWithCategory implements android.os.Parcelable {
    @androidx.room.Embedded()
    @org.jetbrains.annotations.NotNull()
    private final com.expensetracker.data.entity.Expense expense = null;
    @androidx.room.Relation(parentColumn = "category_id", entityColumn = "id")
    @org.jetbrains.annotations.NotNull()
    private final com.expensetracker.data.entity.Category category = null;
    
    public ExpenseWithCategory(@org.jetbrains.annotations.NotNull()
    com.expensetracker.data.entity.Expense expense, @org.jetbrains.annotations.NotNull()
    com.expensetracker.data.entity.Category category) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.expensetracker.data.entity.Expense getExpense() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.expensetracker.data.entity.Category getCategory() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getFormattedAmountWithCurrency(@org.jetbrains.annotations.NotNull()
    java.lang.String currencySymbol) {
        return null;
    }
    
    public final int getCategoryColorInt() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDisplayText() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.expensetracker.data.entity.Expense component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.expensetracker.data.entity.Category component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.expensetracker.data.entity.ExpenseWithCategory copy(@org.jetbrains.annotations.NotNull()
    com.expensetracker.data.entity.Expense expense, @org.jetbrains.annotations.NotNull()
    com.expensetracker.data.entity.Category category) {
        return null;
    }
    
    @java.lang.Override()
    public int describeContents() {
        return 0;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @java.lang.Override()
    public void writeToParcel(@org.jetbrains.annotations.NotNull()
    android.os.Parcel parcel, int flags) {
    }
}