@echo off
echo 🧹 BookKeeping2025 清理和重建腳本
echo =====================================

echo.
echo 📋 第1步：清理專案...
if exist "gradlew.bat" (
    echo 🔧 執行 Gradle 清理...
    gradlew.bat clean
    if %errorlevel% neq 0 (
        echo ❌ 清理失敗
        pause
        exit /b 1
    )
    echo ✅ 清理完成
) else (
    echo ⚠️  找不到 gradlew.bat，請使用 Android Studio 清理專案
)

echo.
echo 📋 第2步：刪除快取資料夾...
if exist ".gradle" (
    echo 🗑️  刪除 .gradle 資料夾...
    rmdir /s /q .gradle
)
if exist "app\build" (
    echo 🗑️  刪除 app\build 資料夾...
    rmdir /s /q app\build
)
if exist "build" (
    echo 🗑️  刪除 build 資料夾...
    rmdir /s /q build
)
echo ✅ 快取清理完成

echo.
echo 📋 第3步：重新建置專案...
if exist "gradlew.bat" (
    echo 🔧 執行 Gradle 建置...
    gradlew.bat assembleDebug
    if %errorlevel% equ 0 (
        echo ✅ 建置成功！
        echo 📱 APK 檔案位於：app\build\outputs\apk\debug\app-debug.apk
    ) else (
        echo ❌ 建置失敗
        echo 💡 請檢查錯誤訊息並修正問題
    )
) else (
    echo ⚠️  找不到 gradlew.bat
    echo 💡 請使用 Android Studio 建置專案：
    echo    1. 開啟 Android Studio
    echo    2. 開啟 BookKeeping2025 專案
    echo    3. 點擊 Build ^> Clean Project
    echo    4. 點擊 Build ^> Rebuild Project
)

echo.
echo 📋 建置完成！
pause