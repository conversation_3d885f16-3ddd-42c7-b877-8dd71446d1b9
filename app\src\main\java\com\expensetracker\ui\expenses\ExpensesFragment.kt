package com.expensetracker.ui.expenses

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.expensetracker.R
import com.expensetracker.databinding.FragmentExpensesBinding

// 暫時移除Hilt來測試基本功能
class ExpensesFragment : Fragment() {

    private var _binding: FragmentExpensesBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        Log.d("ExpensesFragment", "onCreateView called")
        _binding = FragmentExpensesBinding.inflate(inflater, container, false)
        val root: View = binding.root

        setupUI()

        return root
    }

    private fun setupUI() {
        Log.d("ExpensesFragment", "setupUI called")
        
        // 設置RecyclerView為空（暫時）
        binding.recyclerExpenses.apply {
            layoutManager = LinearLayoutManager(requireContext())
            visibility = View.GONE
        }
        
        // 顯示空狀態
        binding.layoutEmptyState.visibility = View.VISIBLE
        
        // 測試FAB按鈕
        try {
            binding.fabAddExpense.setOnClickListener {
                Log.d("ExpensesFragment", "FAB clicked - attempting navigation")
                Toast.makeText(requireContext(), "FAB clicked!", Toast.LENGTH_SHORT).show()
                
                try {
                    findNavController().navigate(R.id.navigation_add_expense)
                    Log.d("ExpensesFragment", "Navigation successful")
                } catch (e: Exception) {
                    Log.e("ExpensesFragment", "Navigation failed", e)
                    Toast.makeText(requireContext(), "Navigation failed: " + e.message, Toast.LENGTH_LONG).show()
                }
            }
            Log.d("ExpensesFragment", "FAB click listener set successfully")
        } catch (e: Exception) {
            Log.e("ExpensesFragment", "Error setting up FAB", e)
            Toast.makeText(requireContext(), "FAB setup failed: " + e.message, Toast.LENGTH_LONG).show()
        }
        
        // 測試空狀態按鈕
        try {
            binding.buttonAddFirstExpense.setOnClickListener {
                Log.d("ExpensesFragment", "Empty state button clicked")
                Toast.makeText(requireContext(), "Empty state button clicked!", Toast.LENGTH_SHORT).show()
                
                try {
                    findNavController().navigate(R.id.navigation_add_expense)
                    Log.d("ExpensesFragment", "Navigation from empty state successful")
                } catch (e: Exception) {
                    Log.e("ExpensesFragment", "Navigation from empty state failed", e)
                    Toast.makeText(requireContext(), "Navigation failed: " + e.message, Toast.LENGTH_LONG).show()
                }
            }
            Log.d("ExpensesFragment", "Empty state button click listener set successfully")
        } catch (e: Exception) {
            Log.e("ExpensesFragment", "Error setting up empty state button", e)
            Toast.makeText(requireContext(), "Empty state button setup failed: " + e.message, Toast.LENGTH_LONG).show()
        }
    }

    override fun onDestroyView() {
        Log.d("ExpensesFragment", "onDestroyView called")
        super.onDestroyView()
        _binding = null
    }
}