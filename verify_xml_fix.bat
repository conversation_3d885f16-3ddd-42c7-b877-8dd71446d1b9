@echo off
echo Verifying XML fixes for BookKeeping2025...
echo ==========================================

echo.
echo Checking item_category.xml for proper XML entities...
findstr /n "Food &amp; Dining" app\src\main\res\layout\item_category.xml
if %errorlevel% == 0 (
    echo ✅ item_category.xml - Fixed: Found "Food &amp; Dining"
) else (
    echo ❌ item_category.xml - Issue: "Food &amp; Dining" not found
)

echo.
echo Checking item_expense.xml for proper XML entities...
findstr /n "Food &amp; Dining" app\src\main\res\layout\item_expense.xml
if %errorlevel% == 0 (
    echo ✅ item_expense.xml - Fixed: Found "Food &amp; Dining"
) else (
    echo ❌ item_expense.xml - Issue: "Food &amp; Dining" not found
)

echo.
echo Checking for any remaining unescaped ampersands...
findstr /r "Food & [^a]" app\src\main\res\layout\*.xml
if %errorlevel% == 1 (
    echo ✅ No unescaped ampersands found in layout files
) else (
    echo ❌ Found unescaped ampersands - need to fix
)

echo.
echo ==========================================
echo XML fix verification complete!
echo.
echo Next step: Run gradle build to test
echo   gradle wrapper --gradle-version 8.9
echo   gradlew assembleDebug
echo.
pause