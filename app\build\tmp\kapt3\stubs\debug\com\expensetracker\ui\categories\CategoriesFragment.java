package com.expensetracker.ui.categories;

@dagger.hilt.android.AndroidEntryPoint()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J$\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u00152\b\u0010\u0016\u001a\u0004\u0018\u00010\u0017H\u0016J\b\u0010\u0018\u001a\u00020\u0019H\u0016J\b\u0010\u001a\u001a\u00020\u0019H\u0002J\b\u0010\u001b\u001a\u00020\u0019H\u0002J\b\u0010\u001c\u001a\u00020\u0019H\u0002J\u0010\u0010\u001d\u001a\u00020\u00192\u0006\u0010\u001e\u001a\u00020\u001fH\u0002R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0005\u001a\u00020\u00048BX\u0082\u0004\u00a2\u0006\u0006\u001a\u0004\b\u0006\u0010\u0007R\u001b\u0010\b\u001a\u00020\t8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\f\u0010\r\u001a\u0004\b\n\u0010\u000bR\u000e\u0010\u000e\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006 "}, d2 = {"Lcom/expensetracker/ui/categories/CategoriesFragment;", "Landroidx/fragment/app/Fragment;", "()V", "_binding", "Lcom/expensetracker/databinding/FragmentCategoriesBinding;", "binding", "getBinding", "()Lcom/expensetracker/databinding/FragmentCategoriesBinding;", "categoriesViewModel", "Lcom/expensetracker/ui/categories/CategoriesViewModel;", "getCategoriesViewModel", "()Lcom/expensetracker/ui/categories/CategoriesViewModel;", "categoriesViewModel$delegate", "Lerror/NonExistentClass;", "categoryAdapter", "Lcom/expensetracker/ui/adapter/CategoryAdapter;", "onCreateView", "Landroid/view/View;", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onDestroyView", "", "setupObservers", "setupRecyclerView", "setupUI", "updateEmptyStateVisibility", "isEmpty", "", "app_debug"})
public final class CategoriesFragment extends androidx.fragment.app.Fragment {
    @org.jetbrains.annotations.Nullable()
    private com.expensetracker.databinding.FragmentCategoriesBinding _binding;
    @org.jetbrains.annotations.NotNull()
    private final error.NonExistentClass categoriesViewModel$delegate = null;
    private com.expensetracker.ui.adapter.CategoryAdapter categoryAdapter;
    
    public CategoriesFragment() {
        super();
    }
    
    private final com.expensetracker.databinding.FragmentCategoriesBinding getBinding() {
        return null;
    }
    
    private final com.expensetracker.ui.categories.CategoriesViewModel getCategoriesViewModel() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    private final void setupRecyclerView() {
    }
    
    private final void setupUI() {
    }
    
    private final void setupObservers() {
    }
    
    private final void updateEmptyStateVisibility(boolean isEmpty) {
    }
    
    @java.lang.Override()
    public void onDestroyView() {
    }
}