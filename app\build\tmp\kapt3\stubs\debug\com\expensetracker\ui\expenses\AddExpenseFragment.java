package com.expensetracker.ui.expenses;

@dagger.hilt.android.AndroidEntryPoint()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\n\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J$\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u001b\u001a\u00020\u001c2\b\u0010\u001d\u001a\u0004\u0018\u00010\u001e2\b\u0010\u001f\u001a\u0004\u0018\u00010 H\u0016J\b\u0010!\u001a\u00020\"H\u0016J\u001a\u0010#\u001a\u00020\"2\u0006\u0010$\u001a\u00020\u001a2\b\u0010\u001f\u001a\u0004\u0018\u00010 H\u0016J\b\u0010%\u001a\u00020\"H\u0002J\u0016\u0010&\u001a\u00020\"2\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000fH\u0002J\b\u0010\'\u001a\u00020\"H\u0002J\b\u0010(\u001a\u00020\"H\u0002J\b\u0010)\u001a\u00020\"H\u0002J\b\u0010*\u001a\u00020\"H\u0002J\b\u0010+\u001a\u00020\"H\u0002R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u0005\u001a\u00020\u00068BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\t\u0010\n\u001a\u0004\b\u0007\u0010\bR\u0014\u0010\u000b\u001a\u00020\u00048BX\u0082\u0004\u00a2\u0006\u0006\u001a\u0004\b\f\u0010\rR\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0012\u0010\u0013\u001a\u0004\u0018\u00010\u0014X\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010\u0015R\u000e\u0010\u0016\u001a\u00020\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006,"}, d2 = {"Lcom/expensetracker/ui/expenses/AddExpenseFragment;", "Landroidx/fragment/app/Fragment;", "()V", "_binding", "Lcom/expensetracker/databinding/FragmentAddExpenseBinding;", "addExpenseViewModel", "Lcom/expensetracker/ui/expenses/AddExpenseViewModel;", "getAddExpenseViewModel", "()Lcom/expensetracker/ui/expenses/AddExpenseViewModel;", "addExpenseViewModel$delegate", "Lerror/NonExistentClass;", "binding", "getBinding", "()Lcom/expensetracker/databinding/FragmentAddExpenseBinding;", "categories", "", "Lcom/expensetracker/data/entity/Category;", "dateFormat", "Ljava/text/SimpleDateFormat;", "selectedCategoryId", "", "Ljava/lang/Long;", "selectedDate", "selectedPaymentMethod", "Lcom/expensetracker/data/entity/PaymentMethod;", "onCreateView", "Landroid/view/View;", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onDestroyView", "", "onViewCreated", "view", "saveExpense", "setupCategoryDropdown", "setupDatePicker", "setupObservers", "setupPaymentMethodDropdown", "setupUI", "showDatePicker", "app_debug"})
public final class AddExpenseFragment extends androidx.fragment.app.Fragment {
    @org.jetbrains.annotations.Nullable()
    private com.expensetracker.databinding.FragmentAddExpenseBinding _binding;
    @org.jetbrains.annotations.NotNull()
    private final error.NonExistentClass addExpenseViewModel$delegate = null;
    private long selectedDate;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Long selectedCategoryId;
    @org.jetbrains.annotations.NotNull()
    private com.expensetracker.data.entity.PaymentMethod selectedPaymentMethod = com.expensetracker.data.entity.PaymentMethod.CASH;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<com.expensetracker.data.entity.Category> categories;
    @org.jetbrains.annotations.NotNull()
    private final java.text.SimpleDateFormat dateFormat = null;
    
    public AddExpenseFragment() {
        super();
    }
    
    private final com.expensetracker.databinding.FragmentAddExpenseBinding getBinding() {
        return null;
    }
    
    private final com.expensetracker.ui.expenses.AddExpenseViewModel getAddExpenseViewModel() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupUI() {
    }
    
    private final void setupObservers() {
    }
    
    private final void setupCategoryDropdown(java.util.List<com.expensetracker.data.entity.Category> categories) {
    }
    
    private final void setupPaymentMethodDropdown() {
    }
    
    private final void setupDatePicker() {
    }
    
    private final void showDatePicker() {
    }
    
    private final void saveExpense() {
    }
    
    @java.lang.Override()
    public void onDestroyView() {
    }
}