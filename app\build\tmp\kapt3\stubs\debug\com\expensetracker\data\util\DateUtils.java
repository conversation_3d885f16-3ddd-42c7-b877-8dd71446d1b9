package com.expensetracker.data.util;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\b\t\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u0004J\u0018\u0010\b\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u0004J\u0018\u0010\t\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u0004J\u0006\u0010\n\u001a\u00020\u0006J\u000e\u0010\u000b\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0006J\u000e\u0010\f\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0006J\u000e\u0010\r\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0006J\u000e\u0010\u000e\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0006\u00a8\u0006\u000f"}, d2 = {"Lcom/expensetracker/data/util/DateUtils;", "", "()V", "formatDate", "", "timestamp", "", "pattern", "formatDateTime", "formatTime", "getCurrentTimestamp", "getEndOfDay", "getEndOfMonth", "getStartOfDay", "getStartOfMonth", "app_debug"})
public final class DateUtils {
    @org.jetbrains.annotations.NotNull()
    public static final com.expensetracker.data.util.DateUtils INSTANCE = null;
    
    private DateUtils() {
        super();
    }
    
    public final long getCurrentTimestamp() {
        return 0L;
    }
    
    public final long getStartOfDay(long timestamp) {
        return 0L;
    }
    
    public final long getEndOfDay(long timestamp) {
        return 0L;
    }
    
    public final long getStartOfMonth(long timestamp) {
        return 0L;
    }
    
    public final long getEndOfMonth(long timestamp) {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String formatDate(long timestamp, @org.jetbrains.annotations.NotNull()
    java.lang.String pattern) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String formatTime(long timestamp, @org.jetbrains.annotations.NotNull()
    java.lang.String pattern) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String formatDateTime(long timestamp, @org.jetbrains.annotations.NotNull()
    java.lang.String pattern) {
        return null;
    }
}