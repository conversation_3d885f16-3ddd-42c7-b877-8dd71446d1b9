<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>
    
    <!-- App specific colors -->
    <color name="primary_green">#FF4CAF50</color>
    <color name="primary_green_dark">#FF388E3C</color>
    <color name="accent_orange">#FFFF9800</color>
    <color name="accent_orange_dark">#FFF57C00</color>
    
    <!-- Expense categories colors -->
    <color name="category_food">#FFFF5722</color>
    <color name="category_transport">#FF2196F3</color>
    <color name="category_entertainment">#FF9C27B0</color>
    <color name="category_shopping">#FFFF9800</color>
    <color name="category_health">#FF4CAF50</color>
    <color name="category_utilities">#FF607D8B</color>
    <color name="category_education">#FF3F51B5</color>
    <color name="category_other">#FF795548</color>
    
    <!-- Status colors -->
    <color name="success_green">#FF4CAF50</color>
    <color name="error_red">#FFF44336</color>
    <color name="warning_yellow">#FFFFC107</color>
    <color name="info_blue">#FF2196F3</color>
    
    <!-- Background colors -->
    <color name="background_light">#FFFAFAFA</color>
    <color name="background_dark">#FF121212</color>
    <color name="surface_light">#FFFFFFFF</color>
    <color name="surface_dark">#FF1E1E1E</color>
</resources>