#!/usr/bin/env python3
"""
建置成功後的測試腳本
"""
import os
import subprocess
import sys

def check_apk_exists():
    """檢查APK檔案是否存在"""
    apk_path = "app/build/outputs/apk/debug/app-debug.apk"
    if os.path.exists(apk_path):
        size = os.path.getsize(apk_path)
        print(f"✅ APK檔案存在: {apk_path}")
        print(f"📦 檔案大小: {size / 1024 / 1024:.2f} MB")
        return True
    else:
        print(f"❌ APK檔案不存在: {apk_path}")
        return False

def check_emulator():
    """檢查模擬器狀態"""
    try:
        result = subprocess.run(["adb", "devices"], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            devices = [line for line in lines if 'device' in line and 'List of devices' not in line]
            
            if devices:
                print(f"✅ 找到 {len(devices)} 個裝置/模擬器:")
                for device in devices:
                    print(f"   📱 {device}")
                return True
            else:
                print("⚠️  沒有找到運行中的模擬器")
                return False
        else:
            print("❌ 無法執行adb命令")
            return False
    except FileNotFoundError:
        print("❌ 找不到adb工具")
        return False

def install_app():
    """安裝應用程式"""
    apk_path = "app/build/outputs/apk/debug/app-debug.apk"
    try:
        print("🔧 安裝應用程式到模擬器...")
        result = subprocess.run(["adb", "install", "-r", apk_path], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 應用程式安裝成功")
            return True
        else:
            print("❌ 應用程式安裝失敗")
            print(f"錯誤: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 安裝過程中發生錯誤: {e}")
        return False

def launch_app():
    """啟動應用程式"""
    try:
        print("🚀 啟動應用程式...")
        result = subprocess.run([
            "adb", "shell", "am", "start", 
            "-n", "com.expensetracker/.MainActivity"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 應用程式啟動成功")
            return True
        else:
            print("❌ 應用程式啟動失敗")
            print(f"錯誤: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 啟動過程中發生錯誤: {e}")
        return False

def show_test_instructions():
    """顯示測試指示"""
    print("\n" + "="*60)
    print("📱 新增記帳功能測試指南")
    print("="*60)
    print()
    print("🎯 現在請在模擬器中測試以下功能：")
    print()
    print("1. 📱 確認應用程式已開啟")
    print("2. 🏠 檢查主畫面是否正常顯示")
    print("3. 📊 點擊底部導航的 'Expenses' 分頁")
    print("4. ➕ 點擊右下角的 FAB 新增按鈕")
    print("5. 📝 填寫新增記帳表單：")
    print("   - 金額: 25.99")
    print("   - 描述: 午餐 - 麥當勞")
    print("   - 分類: 選擇一個分類")
    print("   - 日期: 選擇今天")
    print("   - 付款方式: Cash")
    print("   - 備註: 很好吃 (選填)")
    print("   - 地點: 台北市 (選填)")
    print("6. 💾 點擊 'Save' 按鈕")
    print("7. ✅ 確認成功訊息並返回列表")
    print()
    print("🧪 重點測試項目：")
    print("✅ 表單驗證：嘗試提交空白資料")
    print("✅ 日期選擇：測試日期選擇器")
    print("✅ 下拉選單：測試分類和付款方式")
    print("✅ 取消功能：測試取消按鈕")
    print("✅ 成功回饋：確認成功訊息")
    print("✅ 資料顯示：確認新記錄出現在列表")
    print()

def main():
    """主要測試流程"""
    print("🎉 BookKeeping2025 建置成功測試")
    print("="*50)
    
    # 檢查APK
    if not check_apk_exists():
        print("💡 請先確認建置成功並生成APK檔案")
        return 1
    
    # 檢查模擬器
    emulator_ready = check_emulator()
    
    if not emulator_ready:
        print("\n💡 請先啟動Android模擬器：")
        print("1. 開啟 Android Studio")
        print("2. 點擊 Device Manager")
        print("3. 啟動模擬器")
        print("4. 重新執行此腳本")
        return 1
    
    # 安裝應用程式
    if install_app():
        # 啟動應用程式
        if launch_app():
            show_test_instructions()
            print("\n🎉 測試準備完成！")
            print("現在可以在模擬器中測試新增記帳功能了。")
            return 0
        else:
            print("\n❌ 應用程式啟動失敗")
            return 1
    else:
        print("\n❌ 應用程式安裝失敗")
        return 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n⚠️  測試被使用者中斷")
        sys.exit(1)