package com.expensetracker.ui.expenses;

import com.expensetracker.data.repository.ExpenseRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ExpensesViewModel_Factory implements Factory<ExpensesViewModel> {
  private final Provider<ExpenseRepository> expenseRepositoryProvider;

  public ExpensesViewModel_Factory(Provider<ExpenseRepository> expenseRepositoryProvider) {
    this.expenseRepositoryProvider = expenseRepositoryProvider;
  }

  @Override
  public ExpensesViewModel get() {
    return newInstance(expenseRepositoryProvider.get());
  }

  public static ExpensesViewModel_Factory create(
      Provider<ExpenseRepository> expenseRepositoryProvider) {
    return new ExpensesViewModel_Factory(expenseRepositoryProvider);
  }

  public static ExpensesViewModel newInstance(ExpenseRepository expenseRepository) {
    return new ExpensesViewModel(expenseRepository);
  }
}
