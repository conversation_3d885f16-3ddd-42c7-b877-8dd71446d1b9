package com.expensetracker.data.util;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0005\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u0007J\u000e\u0010\u000b\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tJ\u000e\u0010\f\u001a\u00020\r2\u0006\u0010\b\u001a\u00020\tJ\u0015\u0010\u000e\u001a\u0004\u0018\u00010\t2\u0006\u0010\u000f\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\u0010J\u000e\u0010\u0011\u001a\u00020\t2\u0006\u0010\b\u001a\u00020\tR\u0016\u0010\u0003\u001a\n \u0005*\u0004\u0018\u00010\u00040\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0012"}, d2 = {"Lcom/expensetracker/data/util/CurrencyUtils;", "", "()V", "currencyFormat", "Ljava/text/NumberFormat;", "kotlin.jvm.PlatformType", "formatAmount", "", "amount", "", "currencySymbol", "formatAmountWithLocale", "isValidAmount", "", "parseAmount", "amountString", "(Ljava/lang/String;)Ljava/lang/Double;", "roundToTwoDecimals", "app_debug"})
public final class CurrencyUtils {
    private static final java.text.NumberFormat currencyFormat = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.expensetracker.data.util.CurrencyUtils INSTANCE = null;
    
    private CurrencyUtils() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String formatAmount(double amount, @org.jetbrains.annotations.NotNull()
    java.lang.String currencySymbol) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String formatAmountWithLocale(double amount) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double parseAmount(@org.jetbrains.annotations.NotNull()
    java.lang.String amountString) {
        return null;
    }
    
    public final boolean isValidAmount(double amount) {
        return false;
    }
    
    public final double roundToTwoDecimals(double amount) {
        return 0.0;
    }
}