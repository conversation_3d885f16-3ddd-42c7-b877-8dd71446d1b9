package com.expensetracker.ui.adapter

import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.expensetracker.data.entity.ExpenseWithCategory
import com.expensetracker.databinding.ItemExpenseBinding

class RecentExpenseAdapter(
    private val onExpenseClick: (ExpenseWithCategory) -> Unit = {}
) : ListAdapter<ExpenseWithCategory, RecentExpenseAdapter.RecentExpenseViewHolder>(ExpenseDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecentExpenseViewHolder {
        val binding = ItemExpenseBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return RecentExpenseViewHolder(binding)
    }

    override fun onBindViewHolder(holder: RecentExpenseViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class RecentExpenseViewHolder(
        private val binding: ItemExpenseBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(expenseWithCategory: ExpenseWithCategory) {
            val expense = expenseWithCategory.expense
            val category = expenseWithCategory.category

            // 設置費用描述
            binding.textExpenseDescription.text = expense.description

            // 設置類別名稱
            binding.textExpenseCategory.text = category.name

            // 設置相對時間（今天、昨天等）
            binding.textExpenseDate.text = getRelativeTimeString(expense.date)

            // 設置金額
            binding.textExpenseAmount.text = expense.getFormattedAmount()

            // 設置類別顏色指示器
            try {
                val categoryColor = Color.parseColor(category.color)
                binding.viewCategoryColor.setBackgroundColor(categoryColor)
            } catch (e: IllegalArgumentException) {
                binding.viewCategoryColor.setBackgroundColor(Color.parseColor("#4CAF50"))
            }

            // 設置點擊事件
            binding.root.setOnClickListener {
                onExpenseClick(expenseWithCategory)
            }
        }

        private fun getRelativeTimeString(timestamp: Long): String {
            val now = System.currentTimeMillis()
            val diff = now - timestamp
            
            val oneDay = 24 * 60 * 60 * 1000L
            val oneHour = 60 * 60 * 1000L
            val oneMinute = 60 * 1000L

            return when {
                diff < oneMinute -> "剛剛"
                diff < oneHour -> "${diff / oneMinute} 分鐘前"
                diff < oneDay -> "${diff / oneHour} 小時前"
                diff < 2 * oneDay -> "昨天"
                diff < 7 * oneDay -> "${diff / oneDay} 天前"
                else -> {
                    // 超過一週顯示具體日期
                    val expense = getItem(bindingAdapterPosition).expense
                    expense.getFormattedDate("MM/dd")
                }
            }
        }
    }

    private class ExpenseDiffCallback : DiffUtil.ItemCallback<ExpenseWithCategory>() {
        override fun areItemsTheSame(
            oldItem: ExpenseWithCategory,
            newItem: ExpenseWithCategory
        ): Boolean {
            return oldItem.expense.id == newItem.expense.id
        }

        override fun areContentsTheSame(
            oldItem: ExpenseWithCategory,
            newItem: ExpenseWithCategory
        ): Boolean {
            return oldItem.expense == newItem.expense && oldItem.category == newItem.category
        }
    }
}