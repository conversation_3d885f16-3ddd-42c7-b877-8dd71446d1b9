<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".ui.home.HomeFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Welcome Section -->
        <com.google.android.material.card.MaterialCardView
            style="@style/AppCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/welcome_title"
                    android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                    android:textColor="?attr/colorPrimary" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/welcome_subtitle"
                    android:textAppearance="@style/TextAppearance.Material3.BodyMedium" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Summary Cards -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="horizontal"
            android:weightSum="2">

            <!-- Total Expenses Card -->
            <com.google.android.material.card.MaterialCardView
                style="@style/AppCard"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/total_expenses_label"
                        android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                        android:textColor="?attr/colorOnSurfaceVariant" />

                    <TextView
                        android:id="@+id/text_total_expenses"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="$0.00"
                        android:textAppearance="@style/TextAppearance.Material3.HeadlineMedium"
                        android:textColor="?attr/colorPrimary" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Monthly Expenses Card -->
            <com.google.android.material.card.MaterialCardView
                style="@style/AppCard"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_weight="1">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/this_month_label"
                        android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                        android:textColor="?attr/colorOnSurfaceVariant" />

                    <TextView
                        android:id="@+id/text_monthly_expenses"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="$0.00"
                        android:textAppearance="@style/TextAppearance.Material3.HeadlineMedium"
                        android:textColor="?attr/colorSecondary" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

        <!-- Quick Actions -->
        <com.google.android.material.card.MaterialCardView
            style="@style/AppCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/quick_actions"
                    android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                    android:textColor="?attr/colorOnSurface" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/button_add_expense"
                        style="@style/AppButton.Primary"
                        android:layout_width="0dp"
                        android:layout_marginEnd="8dp"
                        android:layout_weight="1"
                        android:text="@string/add_expense"
                        app:icon="@drawable/ic_add_24" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/button_view_all_expenses"
                        style="@style/AppButton.Secondary"
                        android:layout_width="0dp"
                        android:layout_marginStart="8dp"
                        android:layout_weight="1"
                        android:text="@string/view_all"
                        app:icon="@drawable/ic_list_24" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Recent Expenses -->
        <com.google.android.material.card.MaterialCardView
            style="@style/AppCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/recent_expenses"
                    android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                    android:textColor="?attr/colorOnSurface" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_recent_expenses"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:nestedScrollingEnabled="false"
                    tools:itemCount="3"
                    tools:listitem="@layout/item_expense" />

                <TextView
                    android:id="@+id/text_no_expenses"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:gravity="center"
                    android:text="@string/no_expenses_message"
                    android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:visibility="visible" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView>