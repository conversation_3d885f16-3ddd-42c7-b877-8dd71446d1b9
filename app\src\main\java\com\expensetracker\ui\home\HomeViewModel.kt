package com.expensetracker.ui.home

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.expensetracker.data.entity.Expense
import com.expensetracker.data.entity.ExpenseWithCategory
import com.expensetracker.data.entity.PaymentMethod
import com.expensetracker.data.repository.CategoryRepository
import com.expensetracker.data.repository.ExpenseRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import java.util.Calendar
import javax.inject.Inject

@HiltViewModel
class HomeViewModel @Inject constructor(
    private val expenseRepository: ExpenseRepository,
    private val categoryRepository: CategoryRepository
) : ViewModel() {

    val totalExpenses: LiveData<Double> = expenseRepository.getTotalExpensesLiveData().map { it ?: 0.0 }

    val monthlyExpenses: LiveData<Double> = expenseRepository.getCurrentMonthExpensesLiveData().map { it ?: 0.0 }

    val recentExpenses: LiveData<List<ExpenseWithCategory>> = expenseRepository.getRecentExpensesWithCategory(5)

    private val _isLoading = MutableLiveData<Boolean>().apply {
        value = false
    }
    val isLoading: LiveData<Boolean> = _isLoading

    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage

    init {
        refreshData()
    }

    fun refreshData() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null
                // Data is automatically loaded through LiveData
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "An error occurred while loading data"
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun clearErrorMessage() {
        _errorMessage.value = null
    }
}