<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_expenses" modulePackage="com.expensetracker" filePath="app\src\main\res\layout\fragment_expenses.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/fragment_expenses_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="141" endOffset="53"/></Target><Target id="@+id/edit_search" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="34" startOffset="20" endLine="37" endOffset="62"/></Target><Target id="@+id/button_filter" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="47" startOffset="20" endLine="55" endOffset="59"/></Target><Target id="@+id/button_sort" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="57" startOffset="20" endLine="65" endOffset="57"/></Target><Target id="@+id/recycler_expenses" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="74" startOffset="8" endLine="82" endOffset="51"/></Target><Target id="@+id/layout_empty_state" view="LinearLayout"><Expressions/><location startLine="85" startOffset="8" endLine="127" endOffset="22"/></Target><Target id="@+id/button_add_first_expense" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="119" startOffset="12" endLine="125" endOffset="48"/></Target><Target id="@+id/fab_add_expense" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="132" startOffset="4" endLine="139" endOffset="45"/></Target></Targets></Layout>