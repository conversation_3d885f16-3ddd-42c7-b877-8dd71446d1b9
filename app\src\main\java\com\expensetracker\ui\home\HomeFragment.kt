package com.expensetracker.ui.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.expensetracker.databinding.FragmentHomeBinding

class HomeFragment : Fragment() {

    private var _binding: FragmentHomeBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHomeBinding.inflate(inflater, container, false)
        val root: View = binding.root

        setupUI()

        return root
    }

    private fun setupUI() {
        // 暫時顯示靜態資料
        binding.textTotalExpenses.text = "$0.00"
        binding.textMonthlyExpenses.text = "$0.00"
        
        // 隱藏RecyclerView，顯示空狀態
        binding.recyclerRecentExpenses.visibility = View.GONE
        binding.textNoExpenses.visibility = View.VISIBLE
        
        binding.buttonAddExpense.setOnClickListener {
            // TODO: Navigate to add expense screen
        }

        binding.buttonViewAllExpenses.setOnClickListener {
            // TODO: Navigate to expenses list
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}