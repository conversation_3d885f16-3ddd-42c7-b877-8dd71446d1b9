<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_categories" modulePackage="com.expensetracker" filePath="app\src\main\res\layout\fragment_categories.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/fragment_categories_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="114" endOffset="53"/></Target><Target id="@+id/recycler_categories" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="47" startOffset="8" endLine="55" endOffset="52"/></Target><Target id="@+id/layout_empty_state" view="LinearLayout"><Expressions/><location startLine="58" startOffset="8" endLine="100" endOffset="22"/></Target><Target id="@+id/button_add_first_category" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="92" startOffset="12" endLine="98" endOffset="48"/></Target><Target id="@+id/fab_add_category" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="105" startOffset="4" endLine="112" endOffset="45"/></Target></Targets></Layout>