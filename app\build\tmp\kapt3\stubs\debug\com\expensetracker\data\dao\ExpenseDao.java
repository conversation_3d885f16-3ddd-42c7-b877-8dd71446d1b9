package com.expensetracker.data.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000b\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u000e\n\u0002\u0010\u000e\n\u0002\b\u0005\bg\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0016\u0010\t\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0016\u0010\r\u001a\u00020\u00032\u0006\u0010\u000e\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0014\u0010\u000f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00110\u0010H\'J\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00070\u0011H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00140\u00110\u0010H\'J\u0010\u0010\u0015\u001a\u0004\u0018\u00010\u0016H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00180\u0011H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0018\u0010\u0019\u001a\u0004\u0018\u00010\u00072\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u000e\u0010\u001a\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u001e\u0010\u001c\u001a\u00020\u001b2\u0006\u0010\u001d\u001a\u00020\u000b2\u0006\u0010\u001e\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\u001fJ\u0018\u0010 \u001a\u0004\u0018\u00010\u00142\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ$\u0010!\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00110\u00102\u0006\u0010\"\u001a\u00020\u00162\u0006\u0010#\u001a\u00020\u0016H\'J\u001c\u0010$\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00110\u00102\u0006\u0010\u000e\u001a\u00020\u000bH\'J$\u0010%\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00110\u00102\u0006\u0010\u001d\u001a\u00020\u000b2\u0006\u0010\u001e\u001a\u00020\u000bH\'J\u001c\u0010&\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00110\u00102\u0006\u0010\'\u001a\u00020(H\'J\u001c\u0010)\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00140\u00110\u00102\u0006\u0010\u000e\u001a\u00020\u000bH\'J$\u0010*\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00140\u00110\u00102\u0006\u0010\u001d\u001a\u00020\u000b2\u0006\u0010\u001e\u001a\u00020\u000bH\'J\u0010\u0010+\u001a\u0004\u0018\u00010\u0016H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0010\u0010,\u001a\u0004\u0018\u00010\u0016H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u001e\u0010-\u001a\b\u0012\u0004\u0012\u00020.0\u00112\b\b\u0002\u0010/\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0002\u00100J\u001e\u00101\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00140\u00110\u00102\b\b\u0002\u0010/\u001a\u00020\u001bH\'J\u0010\u00102\u001a\u0004\u0018\u00010\u0016H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0018\u00103\u001a\u0004\u0018\u00010\u00162\u0006\u0010\u000e\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ \u00104\u001a\u0004\u0018\u00010\u00162\u0006\u0010\u001d\u001a\u00020\u000b2\u0006\u0010\u001e\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\u001fJ \u00105\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00160\u00102\u0006\u0010\u001d\u001a\u00020\u000b2\u0006\u0010\u001e\u001a\u00020\u000bH\'J\u0010\u00106\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00160\u0010H\'J\u0016\u00107\u001a\u00020\u000b2\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\"\u00108\u001a\b\u0012\u0004\u0012\u00020\u000b0\u00112\f\u00109\u001a\b\u0012\u0004\u0012\u00020\u00070\u0011H\u00a7@\u00a2\u0006\u0002\u0010:J\u001c\u0010;\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00110\u00102\u0006\u0010<\u001a\u00020=H\'J\u001c\u0010>\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00140\u00110\u00102\u0006\u0010<\u001a\u00020=H\'J\u0016\u0010?\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ \u0010@\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010A\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\u001f\u00a8\u0006B"}, d2 = {"Lcom/expensetracker/data/dao/ExpenseDao;", "", "deleteAllExpenses", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteExpense", "expense", "Lcom/expensetracker/data/entity/Expense;", "(Lcom/expensetracker/data/entity/Expense;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteExpenseById", "expenseId", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteExpensesByCategory", "categoryId", "getAllExpenses", "Landroidx/lifecycle/LiveData;", "", "getAllExpensesSync", "getAllExpensesWithCategory", "Lcom/expensetracker/data/entity/ExpenseWithCategory;", "getAverageExpenseAmount", "", "getCategoryExpenseSummary", "Lcom/expensetracker/data/dao/CategoryExpenseSummary;", "getExpenseById", "getExpenseCount", "", "getExpenseCountByDateRange", "startDate", "endDate", "(JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getExpenseWithCategoryById", "getExpensesByAmountRange", "minAmount", "maxAmount", "getExpensesByCategory", "getExpensesByDateRange", "getExpensesByPaymentMethod", "paymentMethod", "Lcom/expensetracker/data/entity/PaymentMethod;", "getExpensesWithCategoryByCategory", "getExpensesWithCategoryByDateRange", "getMaxExpenseAmount", "getMinExpenseAmount", "getMonthlyExpenseSummary", "Lcom/expensetracker/data/dao/MonthlyExpenseSummary;", "limit", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getRecentExpensesWithCategory", "getTotalExpenses", "getTotalExpensesByCategory", "getTotalExpensesByDateRange", "getTotalExpensesByDateRangeLiveData", "getTotalExpensesLiveData", "insertExpense", "insertExpenses", "expenses", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchExpenses", "searchQuery", "", "searchExpensesWithCategory", "updateExpense", "updateExpenseTimestamp", "timestamp", "app_debug"})
@androidx.room.Dao()
public abstract interface ExpenseDao {
    
    @androidx.room.Query(value = "SELECT * FROM expenses ORDER BY date DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.expensetracker.data.entity.Expense>> getAllExpenses();
    
    @androidx.room.Query(value = "SELECT * FROM expenses ORDER BY date DESC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllExpensesSync(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.expensetracker.data.entity.Expense>> $completion);
    
    @androidx.room.Transaction()
    @androidx.room.Query(value = "SELECT * FROM expenses ORDER BY date DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.expensetracker.data.entity.ExpenseWithCategory>> getAllExpensesWithCategory();
    
    @androidx.room.Transaction()
    @androidx.room.Query(value = "SELECT * FROM expenses ORDER BY date DESC LIMIT :limit")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.expensetracker.data.entity.ExpenseWithCategory>> getRecentExpensesWithCategory(int limit);
    
    @androidx.room.Query(value = "SELECT * FROM expenses WHERE id = :expenseId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getExpenseById(long expenseId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.expensetracker.data.entity.Expense> $completion);
    
    @androidx.room.Transaction()
    @androidx.room.Query(value = "SELECT * FROM expenses WHERE id = :expenseId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getExpenseWithCategoryById(long expenseId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.expensetracker.data.entity.ExpenseWithCategory> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM expenses WHERE category_id = :categoryId ORDER BY date DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.expensetracker.data.entity.Expense>> getExpensesByCategory(long categoryId);
    
    @androidx.room.Transaction()
    @androidx.room.Query(value = "SELECT * FROM expenses WHERE category_id = :categoryId ORDER BY date DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.expensetracker.data.entity.ExpenseWithCategory>> getExpensesWithCategoryByCategory(long categoryId);
    
    @androidx.room.Query(value = "SELECT * FROM expenses WHERE date BETWEEN :startDate AND :endDate ORDER BY date DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.expensetracker.data.entity.Expense>> getExpensesByDateRange(long startDate, long endDate);
    
    @androidx.room.Transaction()
    @androidx.room.Query(value = "SELECT * FROM expenses WHERE date BETWEEN :startDate AND :endDate ORDER BY date DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.expensetracker.data.entity.ExpenseWithCategory>> getExpensesWithCategoryByDateRange(long startDate, long endDate);
    
    @androidx.room.Query(value = "SELECT * FROM expenses WHERE amount BETWEEN :minAmount AND :maxAmount ORDER BY date DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.expensetracker.data.entity.Expense>> getExpensesByAmountRange(double minAmount, double maxAmount);
    
    @androidx.room.Query(value = "SELECT * FROM expenses WHERE payment_method = :paymentMethod ORDER BY date DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.expensetracker.data.entity.Expense>> getExpensesByPaymentMethod(@org.jetbrains.annotations.NotNull()
    com.expensetracker.data.entity.PaymentMethod paymentMethod);
    
    @androidx.room.Query(value = "SELECT * FROM expenses WHERE description LIKE \'%\' || :searchQuery || \'%\' OR notes LIKE \'%\' || :searchQuery || \'%\' ORDER BY date DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.expensetracker.data.entity.Expense>> searchExpenses(@org.jetbrains.annotations.NotNull()
    java.lang.String searchQuery);
    
    @androidx.room.Transaction()
    @androidx.room.Query(value = "SELECT * FROM expenses WHERE description LIKE \'%\' || :searchQuery || \'%\' OR notes LIKE \'%\' || :searchQuery || \'%\' ORDER BY date DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.expensetracker.data.entity.ExpenseWithCategory>> searchExpensesWithCategory(@org.jetbrains.annotations.NotNull()
    java.lang.String searchQuery);
    
    @androidx.room.Query(value = "SELECT SUM(amount) FROM expenses")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTotalExpenses(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT SUM(amount) FROM expenses")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.lang.Double> getTotalExpensesLiveData();
    
    @androidx.room.Query(value = "SELECT SUM(amount) FROM expenses WHERE date BETWEEN :startDate AND :endDate")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTotalExpensesByDateRange(long startDate, long endDate, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT SUM(amount) FROM expenses WHERE date BETWEEN :startDate AND :endDate")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.lang.Double> getTotalExpensesByDateRangeLiveData(long startDate, long endDate);
    
    @androidx.room.Query(value = "SELECT SUM(amount) FROM expenses WHERE category_id = :categoryId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTotalExpensesByCategory(long categoryId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM expenses")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getExpenseCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM expenses WHERE date BETWEEN :startDate AND :endDate")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getExpenseCountByDateRange(long startDate, long endDate, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT AVG(amount) FROM expenses")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAverageExpenseAmount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT MAX(amount) FROM expenses")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getMaxExpenseAmount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT MIN(amount) FROM expenses")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getMinExpenseAmount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "\n        SELECT \n            strftime(\'%Y-%m\', datetime(date/1000, \'unixepoch\')) as month,\n            SUM(amount) as total_amount,\n            COUNT(*) as expense_count\n        FROM expenses \n        GROUP BY strftime(\'%Y-%m\', datetime(date/1000, \'unixepoch\'))\n        ORDER BY month DESC\n        LIMIT :limit\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getMonthlyExpenseSummary(int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.expensetracker.data.dao.MonthlyExpenseSummary>> $completion);
    
    @androidx.room.Query(value = "\n        SELECT \n            c.name as category_name,\n            c.color as category_color,\n            SUM(e.amount) as total_amount,\n            COUNT(e.id) as expense_count\n        FROM expenses e\n        INNER JOIN categories c ON e.category_id = c.id\n        GROUP BY e.category_id, c.name, c.color\n        ORDER BY total_amount DESC\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getCategoryExpenseSummary(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.expensetracker.data.dao.CategoryExpenseSummary>> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertExpense(@org.jetbrains.annotations.NotNull()
    com.expensetracker.data.entity.Expense expense, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertExpenses(@org.jetbrains.annotations.NotNull()
    java.util.List<com.expensetracker.data.entity.Expense> expenses, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.Long>> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateExpense(@org.jetbrains.annotations.NotNull()
    com.expensetracker.data.entity.Expense expense, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteExpense(@org.jetbrains.annotations.NotNull()
    com.expensetracker.data.entity.Expense expense, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM expenses WHERE id = :expenseId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteExpenseById(long expenseId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM expenses WHERE category_id = :categoryId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteExpensesByCategory(long categoryId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM expenses")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAllExpenses(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE expenses SET updated_at = :timestamp WHERE id = :expenseId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateExpenseTimestamp(long expenseId, long timestamp, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}