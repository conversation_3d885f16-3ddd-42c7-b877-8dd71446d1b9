<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mobile_navigation"
    app:startDestination="@+id/navigation_home">

    <fragment
        android:id="@+id/navigation_home"
        android:name="com.expensetracker.ui.home.HomeFragment"
        android:label="@string/title_home"
        tools:layout="@layout/fragment_home" />

    <fragment
        android:id="@+id/navigation_expenses"
        android:name="com.expensetracker.ui.expenses.ExpensesFragment"
        android:label="@string/title_expenses"
        tools:layout="@layout/fragment_expenses" />

    <fragment
        android:id="@+id/navigation_categories"
        android:name="com.expensetracker.ui.categories.CategoriesFragment"
        android:label="@string/title_categories"
        tools:layout="@layout/fragment_categories" />

    <fragment
        android:id="@+id/navigation_reports"
        android:name="com.expensetracker.ui.reports.ReportsFragment"
        android:label="@string/title_reports"
        tools:layout="@layout/fragment_reports" />

    <fragment
        android:id="@+id/navigation_add_expense"
        android:name="com.expensetracker.ui.expenses.AddExpenseFragment"
        android:label="@string/add_expense"
        tools:layout="@layout/fragment_add_expense" />

</navigation>