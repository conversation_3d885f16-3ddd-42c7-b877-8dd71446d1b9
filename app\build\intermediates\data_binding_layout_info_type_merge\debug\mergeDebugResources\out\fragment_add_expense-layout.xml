<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_add_expense" modulePackage="com.expensetracker" filePath="app\src\main\res\layout\fragment_add_expense.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_add_expense_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="277" endOffset="12"/></Target><Target id="@+id/layout_amount" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="63" startOffset="16" endLine="81" endOffset="71"/></Target><Target id="@+id/edit_amount" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="74" startOffset="20" endLine="79" endOffset="46"/></Target><Target id="@+id/layout_description" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="84" startOffset="16" endLine="104" endOffset="71"/></Target><Target id="@+id/edit_description" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="96" startOffset="20" endLine="102" endOffset="46"/></Target><Target id="@+id/layout_category" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="107" startOffset="16" endLine="124" endOffset="71"/></Target><Target id="@+id/dropdown_category" view="AutoCompleteTextView"><Expressions/><location startLine="117" startOffset="20" endLine="122" endOffset="46"/></Target><Target id="@+id/layout_date" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="127" startOffset="16" endLine="146" endOffset="71"/></Target><Target id="@+id/edit_date" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="137" startOffset="20" endLine="144" endOffset="46"/></Target><Target id="@+id/layout_payment_method" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="149" startOffset="16" endLine="165" endOffset="71"/></Target><Target id="@+id/dropdown_payment_method" view="AutoCompleteTextView"><Expressions/><location startLine="158" startOffset="20" endLine="163" endOffset="46"/></Target><Target id="@+id/layout_notes" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="194" startOffset="16" endLine="214" endOffset="71"/></Target><Target id="@+id/edit_notes" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="205" startOffset="20" endLine="212" endOffset="46"/></Target><Target id="@+id/layout_location" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="217" startOffset="16" endLine="235" endOffset="71"/></Target><Target id="@+id/edit_location" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="227" startOffset="20" endLine="233" endOffset="46"/></Target><Target id="@+id/button_cancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="248" startOffset="12" endLine="256" endOffset="50"/></Target><Target id="@+id/button_save" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="258" startOffset="12" endLine="266" endOffset="49"/></Target></Targets></Layout>