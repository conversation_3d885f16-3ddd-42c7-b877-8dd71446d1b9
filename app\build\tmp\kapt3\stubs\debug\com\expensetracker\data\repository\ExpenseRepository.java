package com.expensetracker.data.repository;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000n\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0010\n\u0002\u0018\u0002\n\u0002\b\u0013\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004Jl\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\u00072\b\b\u0002\u0010\r\u001a\u00020\u00072\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u000b2\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u000b2\b\b\u0002\u0010\u0010\u001a\u00020\u00112\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u000bH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0013\u0010\u0014J\u000e\u0010\u0015\u001a\u00020\u0016H\u0086@\u00a2\u0006\u0002\u0010\u0017J\u0016\u0010\u0018\u001a\u00020\u00162\u0006\u0010\u0019\u001a\u00020\u001aH\u0086@\u00a2\u0006\u0002\u0010\u001bJ\u0016\u0010\u001c\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010\u001eJ\u0016\u0010\u001f\u001a\u00020\u00162\u0006\u0010\f\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010\u001eJ\u0012\u0010 \u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001a0\"0!J\u0014\u0010#\u001a\b\u0012\u0004\u0012\u00020\u001a0\"H\u0086@\u00a2\u0006\u0002\u0010\u0017J\u0012\u0010$\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020%0\"0!J\u000e\u0010&\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\u0017J\u0014\u0010\'\u001a\b\u0012\u0004\u0012\u00020(0\"H\u0086@\u00a2\u0006\u0002\u0010\u0017J\u000e\u0010)\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\u0017J\u000e\u0010*\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0!J\u000e\u0010+\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\u0017J\u0018\u0010,\u001a\u0004\u0018\u00010\u001a2\u0006\u0010\u001d\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010\u001eJ\u000e\u0010-\u001a\u00020.H\u0086@\u00a2\u0006\u0002\u0010\u0017J\u001e\u0010/\u001a\u00020.2\u0006\u00100\u001a\u00020\u00072\u0006\u00101\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u00102J\u0018\u00103\u001a\u0004\u0018\u00010%2\u0006\u0010\u001d\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010\u001eJ\"\u00104\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001a0\"0!2\u0006\u00105\u001a\u00020\t2\u0006\u00106\u001a\u00020\tJ\u001a\u00107\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001a0\"0!2\u0006\u0010\f\u001a\u00020\u0007J\"\u00108\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001a0\"0!2\u0006\u00100\u001a\u00020\u00072\u0006\u00101\u001a\u00020\u0007J\u001a\u00109\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001a0\"0!2\u0006\u0010\u0010\u001a\u00020\u0011J\u001a\u0010:\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020%0\"0!2\u0006\u0010\f\u001a\u00020\u0007J\"\u0010;\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020%0\"0!2\u0006\u00100\u001a\u00020\u00072\u0006\u00101\u001a\u00020\u0007J\u000e\u0010<\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\u0017J\u000e\u0010=\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\u0017J\u001e\u0010>\u001a\b\u0012\u0004\u0012\u00020?0\"2\b\b\u0002\u0010@\u001a\u00020.H\u0086@\u00a2\u0006\u0002\u0010AJ\u001c\u0010B\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020%0\"0!2\b\b\u0002\u0010@\u001a\u00020.J\u000e\u0010C\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\u0017J\u0016\u0010D\u001a\u00020\t2\u0006\u0010\f\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010\u001eJ\u001e\u0010E\u001a\u00020\t2\u0006\u00100\u001a\u00020\u00072\u0006\u00101\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u00102J\u001e\u0010F\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0!2\u0006\u00100\u001a\u00020\u00072\u0006\u00101\u001a\u00020\u0007J\u000e\u0010G\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0!J\u0016\u0010H\u001a\u00020\u00072\u0006\u0010\u0019\u001a\u00020\u001aH\u0086@\u00a2\u0006\u0002\u0010\u001bJ\"\u0010I\u001a\b\u0012\u0004\u0012\u00020\u00070\"2\f\u0010J\u001a\b\u0012\u0004\u0012\u00020\u001a0\"H\u0086@\u00a2\u0006\u0002\u0010KJ\u001a\u0010L\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001a0\"0!2\u0006\u0010M\u001a\u00020\u000bJ\u001a\u0010N\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020%0\"0!2\u0006\u0010M\u001a\u00020\u000bJ\u0016\u0010O\u001a\u00020\u00162\u0006\u0010\u0019\u001a\u00020\u001aH\u0086@\u00a2\u0006\u0002\u0010\u001bJr\u0010O\u001a\b\u0012\u0004\u0012\u00020\u00160\u00062\u0006\u0010\u001d\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\u00072\u0006\u0010\r\u001a\u00020\u00072\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u000b2\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u000b2\b\b\u0002\u0010\u0010\u001a\u00020\u00112\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u000bH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bP\u0010QR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006R"}, d2 = {"Lcom/expensetracker/data/repository/ExpenseRepository;", "", "expenseDao", "Lcom/expensetracker/data/dao/ExpenseDao;", "(Lcom/expensetracker/data/dao/ExpenseDao;)V", "createExpense", "Lkotlin/Result;", "", "amount", "", "description", "", "categoryId", "date", "notes", "location", "paymentMethod", "Lcom/expensetracker/data/entity/PaymentMethod;", "receiptImagePath", "createExpense-tZkwj4A", "(DLjava/lang/String;JJLjava/lang/String;Ljava/lang/String;Lcom/expensetracker/data/entity/PaymentMethod;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteAllExpenses", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteExpense", "expense", "Lcom/expensetracker/data/entity/Expense;", "(Lcom/expensetracker/data/entity/Expense;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteExpenseById", "expenseId", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteExpensesByCategory", "getAllExpenses", "Landroidx/lifecycle/LiveData;", "", "getAllExpensesSync", "getAllExpensesWithCategory", "Lcom/expensetracker/data/entity/ExpenseWithCategory;", "getAverageExpenseAmount", "getCategoryExpenseSummary", "Lcom/expensetracker/data/dao/CategoryExpenseSummary;", "getCurrentMonthExpenses", "getCurrentMonthExpensesLiveData", "getCurrentWeekExpenses", "getExpenseById", "getExpenseCount", "", "getExpenseCountByDateRange", "startDate", "endDate", "(JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getExpenseWithCategoryById", "getExpensesByAmountRange", "minAmount", "maxAmount", "getExpensesByCategory", "getExpensesByDateRange", "getExpensesByPaymentMethod", "getExpensesWithCategoryByCategory", "getExpensesWithCategoryByDateRange", "getMaxExpenseAmount", "getMinExpenseAmount", "getMonthlyExpenseSummary", "Lcom/expensetracker/data/dao/MonthlyExpenseSummary;", "limit", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getRecentExpensesWithCategory", "getTotalExpenses", "getTotalExpensesByCategory", "getTotalExpensesByDateRange", "getTotalExpensesByDateRangeLiveData", "getTotalExpensesLiveData", "insertExpense", "insertExpenses", "expenses", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchExpenses", "searchQuery", "searchExpensesWithCategory", "updateExpense", "updateExpense-LiYkppA", "(JDLjava/lang/String;JJLjava/lang/String;Ljava/lang/String;Lcom/expensetracker/data/entity/PaymentMethod;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class ExpenseRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.expensetracker.data.dao.ExpenseDao expenseDao = null;
    
    @javax.inject.Inject()
    public ExpenseRepository(@org.jetbrains.annotations.NotNull()
    com.expensetracker.data.dao.ExpenseDao expenseDao) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.expensetracker.data.entity.Expense>> getAllExpenses() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.expensetracker.data.entity.ExpenseWithCategory>> getAllExpensesWithCategory() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.expensetracker.data.entity.ExpenseWithCategory>> getRecentExpensesWithCategory(int limit) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.expensetracker.data.entity.Expense>> getExpensesByCategory(long categoryId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.expensetracker.data.entity.ExpenseWithCategory>> getExpensesWithCategoryByCategory(long categoryId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.expensetracker.data.entity.Expense>> getExpensesByDateRange(long startDate, long endDate) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.expensetracker.data.entity.ExpenseWithCategory>> getExpensesWithCategoryByDateRange(long startDate, long endDate) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.expensetracker.data.entity.Expense>> getExpensesByAmountRange(double minAmount, double maxAmount) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.expensetracker.data.entity.Expense>> getExpensesByPaymentMethod(@org.jetbrains.annotations.NotNull()
    com.expensetracker.data.entity.PaymentMethod paymentMethod) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.expensetracker.data.entity.Expense>> searchExpenses(@org.jetbrains.annotations.NotNull()
    java.lang.String searchQuery) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.expensetracker.data.entity.ExpenseWithCategory>> searchExpensesWithCategory(@org.jetbrains.annotations.NotNull()
    java.lang.String searchQuery) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Double> getTotalExpensesLiveData() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Double> getTotalExpensesByDateRangeLiveData(long startDate, long endDate) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getAllExpensesSync(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.expensetracker.data.entity.Expense>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getExpenseById(long expenseId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.expensetracker.data.entity.Expense> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getExpenseWithCategoryById(long expenseId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.expensetracker.data.entity.ExpenseWithCategory> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getTotalExpenses(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getTotalExpensesByDateRange(long startDate, long endDate, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getTotalExpensesByCategory(long categoryId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getExpenseCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getExpenseCountByDateRange(long startDate, long endDate, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getAverageExpenseAmount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getMaxExpenseAmount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getMinExpenseAmount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getMonthlyExpenseSummary(int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.expensetracker.data.dao.MonthlyExpenseSummary>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCategoryExpenseSummary(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.expensetracker.data.dao.CategoryExpenseSummary>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object insertExpense(@org.jetbrains.annotations.NotNull()
    com.expensetracker.data.entity.Expense expense, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object insertExpenses(@org.jetbrains.annotations.NotNull()
    java.util.List<com.expensetracker.data.entity.Expense> expenses, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.Long>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateExpense(@org.jetbrains.annotations.NotNull()
    com.expensetracker.data.entity.Expense expense, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteExpense(@org.jetbrains.annotations.NotNull()
    com.expensetracker.data.entity.Expense expense, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteExpenseById(long expenseId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteExpensesByCategory(long categoryId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteAllExpenses(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCurrentMonthExpenses(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Double> getCurrentMonthExpensesLiveData() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCurrentWeekExpenses(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
}