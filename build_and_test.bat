@echo off
echo 🚀 BookKeeping2025 建置和測試腳本
echo =====================================

echo.
echo 📋 第1步：檢查專案結構...
if not exist "app\build.gradle.kts" (
    echo ❌ 找不到 app\build.gradle.kts
    pause
    exit /b 1
)
echo ✅ 專案結構正確

echo.
echo 📋 第2步：檢查Android SDK...
where adb >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  找不到 adb，請確認 Android SDK 已安裝
) else (
    echo ✅ Android SDK 已安裝
)

echo.
echo 📋 第3步：檢查模擬器狀態...
adb devices
echo.

echo 📋 第4步：嘗試建置專案...
echo 注意：如果缺少 gradle-wrapper.jar，此步驟可能會失敗

if exist "gradlew.bat" (
    echo 🔧 使用 gradlew 建置...
    gradlew.bat clean assembleDebug
) else (
    echo ⚠️  找不到 gradlew.bat
    echo 💡 請使用 Android Studio 開啟專案進行建置
)

echo.
echo 📋 建置完成！
echo 💡 如果建置成功，APK 檔案位於：app\build\outputs\apk\debug\
echo.

pause