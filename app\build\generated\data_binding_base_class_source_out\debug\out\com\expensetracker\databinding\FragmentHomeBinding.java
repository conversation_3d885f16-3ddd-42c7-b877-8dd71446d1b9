// Generated by view binder compiler. Do not edit!
package com.expensetracker.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.expensetracker.R;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentHomeBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final MaterialButton buttonAddExpense;

  @NonNull
  public final MaterialButton buttonViewAllExpenses;

  @NonNull
  public final RecyclerView recyclerRecentExpenses;

  @NonNull
  public final TextView textMonthlyExpenses;

  @NonNull
  public final TextView textNoExpenses;

  @NonNull
  public final TextView textTotalExpenses;

  private FragmentHomeBinding(@NonNull ScrollView rootView,
      @NonNull MaterialButton buttonAddExpense, @NonNull MaterialButton buttonViewAllExpenses,
      @NonNull RecyclerView recyclerRecentExpenses, @NonNull TextView textMonthlyExpenses,
      @NonNull TextView textNoExpenses, @NonNull TextView textTotalExpenses) {
    this.rootView = rootView;
    this.buttonAddExpense = buttonAddExpense;
    this.buttonViewAllExpenses = buttonViewAllExpenses;
    this.recyclerRecentExpenses = recyclerRecentExpenses;
    this.textMonthlyExpenses = textMonthlyExpenses;
    this.textNoExpenses = textNoExpenses;
    this.textTotalExpenses = textTotalExpenses;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentHomeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentHomeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_home, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentHomeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_add_expense;
      MaterialButton buttonAddExpense = ViewBindings.findChildViewById(rootView, id);
      if (buttonAddExpense == null) {
        break missingId;
      }

      id = R.id.button_view_all_expenses;
      MaterialButton buttonViewAllExpenses = ViewBindings.findChildViewById(rootView, id);
      if (buttonViewAllExpenses == null) {
        break missingId;
      }

      id = R.id.recycler_recent_expenses;
      RecyclerView recyclerRecentExpenses = ViewBindings.findChildViewById(rootView, id);
      if (recyclerRecentExpenses == null) {
        break missingId;
      }

      id = R.id.text_monthly_expenses;
      TextView textMonthlyExpenses = ViewBindings.findChildViewById(rootView, id);
      if (textMonthlyExpenses == null) {
        break missingId;
      }

      id = R.id.text_no_expenses;
      TextView textNoExpenses = ViewBindings.findChildViewById(rootView, id);
      if (textNoExpenses == null) {
        break missingId;
      }

      id = R.id.text_total_expenses;
      TextView textTotalExpenses = ViewBindings.findChildViewById(rootView, id);
      if (textTotalExpenses == null) {
        break missingId;
      }

      return new FragmentHomeBinding((ScrollView) rootView, buttonAddExpense, buttonViewAllExpenses,
          recyclerRecentExpenses, textMonthlyExpenses, textNoExpenses, textTotalExpenses);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
