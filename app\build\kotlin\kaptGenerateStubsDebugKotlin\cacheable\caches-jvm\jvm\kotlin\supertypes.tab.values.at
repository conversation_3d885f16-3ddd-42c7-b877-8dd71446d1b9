/ Header Record For PersistentHashMapValueStorage android.app.Application) (androidx.appcompat.app.AppCompatActivity androidx.room.RoomDatabase$ #androidx.room.RoomDatabase.Callback android.os.Parcelable android.os.Parcelable kotlin.Enum kotlin.Enum android.os.Parcelable) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback androidx.fragment.app.Fragment androidx.lifecycle.ViewModel androidx.fragment.app.Fragment androidx.lifecycle.ViewModel androidx.fragment.app.Fragment androidx.lifecycle.ViewModel androidx.fragment.app.Fragment androidx.lifecycle.ViewModel androidx.fragment.app.Fragment androidx.lifecycle.ViewModel!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding