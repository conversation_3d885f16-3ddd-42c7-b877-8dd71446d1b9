@echo off
echo 🧹 清理Hilt KSP快取
echo =====================================

echo.
echo 📋 第1步：停止Gradle程序...
taskkill /f /im gradle.exe 2>nul
taskkill /f /im java.exe /fi "WINDOWTITLE eq Gradle*" 2>nul

echo.
echo 📋 第2步：刪除所有建置快取...
if exist ".gradle" (
    echo 🗑️  刪除 .gradle 資料夾...
    rmdir /s /q .gradle
)
if exist "app\build" (
    echo 🗑️  刪除 app\build 資料夾...
    rmdir /s /q app\build
)
if exist "build" (
    echo 🗑️  刪除 build 資料夾...
    rmdir /s /q build
)

echo.
echo 📋 第3步：清理KSP生成的檔案...
if exist "app\build\generated\ksp" (
    echo 🗑️  刪除 KSP 生成檔案...
    rmdir /s /q app\build\generated\ksp
)

echo.
echo 📋 第4步：清理Hilt生成的檔案...
if exist "app\build\generated\source\kapt" (
    echo 🗑️  刪除 KAPT 生成檔案...
    rmdir /s /q app\build\generated\source\kapt
)

echo.
echo 📋 第5步：重新建置專案...
gradlew.bat clean
gradlew.bat assembleDebug

echo.
echo ✅ 清理和重建完成！
pause