<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_reports" modulePackage="com.expensetracker" filePath="app\src\main\res\layout\fragment_reports.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_reports_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="278" endOffset="12"/></Target><Target id="@+id/text_total_expenses" view="TextView"><Expressions/><location startLine="76" startOffset="20" endLine="83" endOffset="64"/></Target><Target id="@+id/text_monthly_expenses" view="TextView"><Expressions/><location startLine="111" startOffset="20" endLine="118" endOffset="66"/></Target><Target id="@+id/text_average_expenses" view="TextView"><Expressions/><location startLine="146" startOffset="20" endLine="153" endOffset="65"/></Target><Target id="@+id/chart_monthly_trend" view="com.github.mikephil.charting.charts.LineChart"><Expressions/><location startLine="182" startOffset="16" endLine="186" endOffset="53"/></Target><Target id="@+id/chart_category_breakdown" view="com.github.mikephil.charting.charts.PieChart"><Expressions/><location startLine="213" startOffset="16" endLine="217" endOffset="53"/></Target><Target id="@+id/button_export_pdf" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="250" startOffset="20" endLine="258" endOffset="56"/></Target><Target id="@+id/button_export_csv" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="260" startOffset="20" endLine="268" endOffset="56"/></Target></Targets></Layout>