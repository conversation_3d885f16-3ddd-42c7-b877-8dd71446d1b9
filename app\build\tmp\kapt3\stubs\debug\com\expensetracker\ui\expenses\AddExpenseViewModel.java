package com.expensetracker.ui.expenses;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\u000b\n\u0002\b\b\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\t\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0006\u0010)\u001a\u00020*J\u0006\u0010+\u001a\u00020*JI\u0010,\u001a\u00020*2\u0006\u0010-\u001a\u00020\t2\u0006\u0010.\u001a\u00020\t2\b\u0010/\u001a\u0004\u0018\u0001002\u0006\u00101\u001a\u0002002\u0006\u00102\u001a\u0002032\b\u00104\u001a\u0004\u0018\u00010\t2\b\u00105\u001a\u0004\u0018\u00010\t\u00a2\u0006\u0002\u00106J\u000e\u00107\u001a\u0002032\u0006\u00108\u001a\u00020\tJ\u0006\u00109\u001a\u00020*J\'\u0010:\u001a\u00020!2\u0006\u0010-\u001a\u00020\t2\u0006\u0010.\u001a\u00020\t2\b\u0010/\u001a\u0004\u0018\u000100H\u0002\u00a2\u0006\u0002\u0010;R\u0016\u0010\u0007\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\n\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u000b\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\f\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\r\u001a\u00020\u000eX\u0082\u0004\u00a2\u0006\u0004\n\u0002\u0010\u000fR\u0010\u0010\u0010\u001a\u00020\u000eX\u0082\u0004\u00a2\u0006\u0004\n\u0002\u0010\u000fR\u0016\u0010\u0011\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u0012\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u001d\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00180\u00170\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0015R\u0019\u0010\u001a\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0015R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u001c\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0015R\u0019\u0010\u001e\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0015R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010 \u001a\b\u0012\u0004\u0012\u00020!0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0015R\u0017\u0010\"\u001a\b\u0012\u0004\u0012\u00020!0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u0015R\u0013\u0010$\u001a\u00020\u000e\u00a2\u0006\n\n\u0002\u0010\u000f\u001a\u0004\b%\u0010&R\u0019\u0010\'\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010\u0015\u00a8\u0006<"}, d2 = {"Lcom/expensetracker/ui/expenses/AddExpenseViewModel;", "Landroidx/lifecycle/ViewModel;", "expenseRepository", "Lcom/expensetracker/data/repository/ExpenseRepository;", "categoryRepository", "Lcom/expensetracker/data/repository/CategoryRepository;", "(Lcom/expensetracker/data/repository/ExpenseRepository;Lcom/expensetracker/data/repository/CategoryRepository;)V", "_amountError", "Landroidx/lifecycle/MutableLiveData;", "", "_categoryError", "_descriptionError", "_errorMessage", "_isLoading", "error/NonExistentClass", "Lerror/NonExistentClass;", "_navigateBack", "_successMessage", "amountError", "Landroidx/lifecycle/LiveData;", "getAmountError", "()Landroidx/lifecycle/LiveData;", "categories", "", "Lcom/expensetracker/data/entity/Category;", "getCategories", "categoryError", "getCategoryError", "descriptionError", "getDescriptionError", "errorMessage", "getErrorMessage", "isLoading", "", "navigateBack", "getNavigateBack", "paymentMethods", "getPaymentMethods", "()Lerror/NonExistentClass;", "successMessage", "getSuccessMessage", "clearErrors", "", "clearMessages", "createExpense", "amount", "description", "categoryId", "", "date", "paymentMethod", "Lcom/expensetracker/data/entity/PaymentMethod;", "notes", "location", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Long;JLcom/expensetracker/data/entity/PaymentMethod;Ljava/lang/String;Ljava/lang/String;)V", "getPaymentMethodFromDisplayName", "displayName", "onNavigateBackComplete", "validateInput", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Long;)Z", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class AddExpenseViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.expensetracker.data.repository.ExpenseRepository expenseRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.expensetracker.data.repository.CategoryRepository categoryRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.util.List<com.expensetracker.data.entity.Category>> categories = null;
    @org.jetbrains.annotations.NotNull()
    private final error.NonExistentClass _isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.Boolean> isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.String> _errorMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.String> errorMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.String> _successMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.String> successMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final error.NonExistentClass _navigateBack = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.Boolean> navigateBack = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.String> _amountError = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.String> amountError = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.String> _descriptionError = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.String> descriptionError = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.String> _categoryError = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.String> categoryError = null;
    @org.jetbrains.annotations.NotNull()
    private final error.NonExistentClass paymentMethods = null;
    
    @javax.inject.Inject()
    public AddExpenseViewModel(@org.jetbrains.annotations.NotNull()
    com.expensetracker.data.repository.ExpenseRepository expenseRepository, @org.jetbrains.annotations.NotNull()
    com.expensetracker.data.repository.CategoryRepository categoryRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.expensetracker.data.entity.Category>> getCategories() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Boolean> isLoading() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.String> getErrorMessage() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.String> getSuccessMessage() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Boolean> getNavigateBack() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.String> getAmountError() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.String> getDescriptionError() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.String> getCategoryError() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final error.NonExistentClass getPaymentMethods() {
        return null;
    }
    
    public final void createExpense(@org.jetbrains.annotations.NotNull()
    java.lang.String amount, @org.jetbrains.annotations.NotNull()
    java.lang.String description, @org.jetbrains.annotations.Nullable()
    java.lang.Long categoryId, long date, @org.jetbrains.annotations.NotNull()
    com.expensetracker.data.entity.PaymentMethod paymentMethod, @org.jetbrains.annotations.Nullable()
    java.lang.String notes, @org.jetbrains.annotations.Nullable()
    java.lang.String location) {
    }
    
    private final boolean validateInput(java.lang.String amount, java.lang.String description, java.lang.Long categoryId) {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.expensetracker.data.entity.PaymentMethod getPaymentMethodFromDisplayName(@org.jetbrains.annotations.NotNull()
    java.lang.String displayName) {
        return null;
    }
    
    public final void clearErrors() {
    }
    
    public final void clearMessages() {
    }
    
    public final void onNavigateBackComplete() {
    }
}