<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.BookKeeping2025" parent="Theme.Material3.DayNight">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary_green</item>
        <item name="colorPrimaryVariant">@color/primary_green_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/accent_orange</item>
        <item name="colorSecondaryVariant">@color/accent_orange_dark</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
    </style>

    <style name="Theme.BookKeeping2025.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="Theme.BookKeeping2025.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="Theme.BookKeeping2025.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />
    
    <!-- Button styles -->
    <style name="AppButton" parent="Widget.Material3.Button">
        <item name="android:layout_height">48dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textAllCaps">false</item>
    </style>
    
    <style name="AppButton.Primary" parent="AppButton">
        <item name="backgroundTint">@color/primary_green</item>
        <item name="android:textColor">@color/white</item>
    </style>
    
    <style name="AppButton.Secondary" parent="AppButton">
        <item name="backgroundTint">@color/accent_orange</item>
        <item name="android:textColor">@color/white</item>
    </style>
    
    <!-- Text input styles -->
    <style name="AppTextInputLayout" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary_green</item>
        <item name="hintTextColor">@color/primary_green</item>
    </style>
    
    <!-- Card styles -->
    <style name="AppCard" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">8dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:layout_margin">8dp</item>
    </style>
</resources>