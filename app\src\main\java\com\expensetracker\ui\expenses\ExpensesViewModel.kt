package com.expensetracker.ui.expenses

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.expensetracker.data.entity.ExpenseWithCategory
import com.expensetracker.data.repository.ExpenseRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ExpensesViewModel @Inject constructor(
    private val expenseRepository: ExpenseRepository
) : ViewModel() {

    val expenses: LiveData<List<ExpenseWithCategory>> = expenseRepository.getAllExpensesWithCategory()

    private val _isLoading = MutableLiveData<Boolean>().apply {
        value = false
    }
    val isLoading: LiveData<Boolean> = _isLoading

    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage

    fun deleteExpense(expenseId: Long) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                expenseRepository.deleteExpenseById(expenseId)
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Error deleting expense"
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun refreshExpenses() {
        // LiveData automatically refreshes, no manual action needed
    }

    fun clearErrorMessage() {
        _errorMessage.value = null
    }
}