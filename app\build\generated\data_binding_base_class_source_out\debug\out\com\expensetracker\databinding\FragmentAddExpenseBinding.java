// Generated by view binder compiler. Do not edit!
package com.expensetracker.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import android.widget.ScrollView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.expensetracker.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentAddExpenseBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final MaterialButton buttonCancel;

  @NonNull
  public final MaterialButton buttonSave;

  @NonNull
  public final AutoCompleteTextView dropdownCategory;

  @NonNull
  public final AutoCompleteTextView dropdownPaymentMethod;

  @NonNull
  public final TextInputEditText editAmount;

  @NonNull
  public final TextInputEditText editDate;

  @NonNull
  public final TextInputEditText editDescription;

  @NonNull
  public final TextInputEditText editLocation;

  @NonNull
  public final TextInputEditText editNotes;

  @NonNull
  public final TextInputLayout layoutAmount;

  @NonNull
  public final TextInputLayout layoutCategory;

  @NonNull
  public final TextInputLayout layoutDate;

  @NonNull
  public final TextInputLayout layoutDescription;

  @NonNull
  public final TextInputLayout layoutLocation;

  @NonNull
  public final TextInputLayout layoutNotes;

  @NonNull
  public final TextInputLayout layoutPaymentMethod;

  private FragmentAddExpenseBinding(@NonNull ScrollView rootView,
      @NonNull MaterialButton buttonCancel, @NonNull MaterialButton buttonSave,
      @NonNull AutoCompleteTextView dropdownCategory,
      @NonNull AutoCompleteTextView dropdownPaymentMethod, @NonNull TextInputEditText editAmount,
      @NonNull TextInputEditText editDate, @NonNull TextInputEditText editDescription,
      @NonNull TextInputEditText editLocation, @NonNull TextInputEditText editNotes,
      @NonNull TextInputLayout layoutAmount, @NonNull TextInputLayout layoutCategory,
      @NonNull TextInputLayout layoutDate, @NonNull TextInputLayout layoutDescription,
      @NonNull TextInputLayout layoutLocation, @NonNull TextInputLayout layoutNotes,
      @NonNull TextInputLayout layoutPaymentMethod) {
    this.rootView = rootView;
    this.buttonCancel = buttonCancel;
    this.buttonSave = buttonSave;
    this.dropdownCategory = dropdownCategory;
    this.dropdownPaymentMethod = dropdownPaymentMethod;
    this.editAmount = editAmount;
    this.editDate = editDate;
    this.editDescription = editDescription;
    this.editLocation = editLocation;
    this.editNotes = editNotes;
    this.layoutAmount = layoutAmount;
    this.layoutCategory = layoutCategory;
    this.layoutDate = layoutDate;
    this.layoutDescription = layoutDescription;
    this.layoutLocation = layoutLocation;
    this.layoutNotes = layoutNotes;
    this.layoutPaymentMethod = layoutPaymentMethod;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentAddExpenseBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentAddExpenseBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_add_expense, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentAddExpenseBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_cancel;
      MaterialButton buttonCancel = ViewBindings.findChildViewById(rootView, id);
      if (buttonCancel == null) {
        break missingId;
      }

      id = R.id.button_save;
      MaterialButton buttonSave = ViewBindings.findChildViewById(rootView, id);
      if (buttonSave == null) {
        break missingId;
      }

      id = R.id.dropdown_category;
      AutoCompleteTextView dropdownCategory = ViewBindings.findChildViewById(rootView, id);
      if (dropdownCategory == null) {
        break missingId;
      }

      id = R.id.dropdown_payment_method;
      AutoCompleteTextView dropdownPaymentMethod = ViewBindings.findChildViewById(rootView, id);
      if (dropdownPaymentMethod == null) {
        break missingId;
      }

      id = R.id.edit_amount;
      TextInputEditText editAmount = ViewBindings.findChildViewById(rootView, id);
      if (editAmount == null) {
        break missingId;
      }

      id = R.id.edit_date;
      TextInputEditText editDate = ViewBindings.findChildViewById(rootView, id);
      if (editDate == null) {
        break missingId;
      }

      id = R.id.edit_description;
      TextInputEditText editDescription = ViewBindings.findChildViewById(rootView, id);
      if (editDescription == null) {
        break missingId;
      }

      id = R.id.edit_location;
      TextInputEditText editLocation = ViewBindings.findChildViewById(rootView, id);
      if (editLocation == null) {
        break missingId;
      }

      id = R.id.edit_notes;
      TextInputEditText editNotes = ViewBindings.findChildViewById(rootView, id);
      if (editNotes == null) {
        break missingId;
      }

      id = R.id.layout_amount;
      TextInputLayout layoutAmount = ViewBindings.findChildViewById(rootView, id);
      if (layoutAmount == null) {
        break missingId;
      }

      id = R.id.layout_category;
      TextInputLayout layoutCategory = ViewBindings.findChildViewById(rootView, id);
      if (layoutCategory == null) {
        break missingId;
      }

      id = R.id.layout_date;
      TextInputLayout layoutDate = ViewBindings.findChildViewById(rootView, id);
      if (layoutDate == null) {
        break missingId;
      }

      id = R.id.layout_description;
      TextInputLayout layoutDescription = ViewBindings.findChildViewById(rootView, id);
      if (layoutDescription == null) {
        break missingId;
      }

      id = R.id.layout_location;
      TextInputLayout layoutLocation = ViewBindings.findChildViewById(rootView, id);
      if (layoutLocation == null) {
        break missingId;
      }

      id = R.id.layout_notes;
      TextInputLayout layoutNotes = ViewBindings.findChildViewById(rootView, id);
      if (layoutNotes == null) {
        break missingId;
      }

      id = R.id.layout_payment_method;
      TextInputLayout layoutPaymentMethod = ViewBindings.findChildViewById(rootView, id);
      if (layoutPaymentMethod == null) {
        break missingId;
      }

      return new FragmentAddExpenseBinding((ScrollView) rootView, buttonCancel, buttonSave,
          dropdownCategory, dropdownPaymentMethod, editAmount, editDate, editDescription,
          editLocation, editNotes, layoutAmount, layoutCategory, layoutDate, layoutDescription,
          layoutLocation, layoutNotes, layoutPaymentMethod);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
