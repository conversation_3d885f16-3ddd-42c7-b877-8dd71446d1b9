package com.expensetracker.data.repository

import androidx.lifecycle.LiveData
import com.expensetracker.data.dao.CategoryDao
import com.expensetracker.data.dao.CategoryWithStats
import com.expensetracker.data.entity.Category
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class CategoryRepository @Inject constructor(
    private val categoryDao: CategoryDao
) {
    
    // LiveData queries
    fun getAllCategories(): LiveData<List<Category>> = categoryDao.getAllCategories()
    
    fun getCategoryById(categoryId: Long): LiveData<Category?> = categoryDao.getCategoryByIdLiveData(categoryId)
    
    fun getCategoriesWithStats(): LiveData<List<CategoryWithStats>> = categoryDao.getCategoriesWithStats()
    
    // Suspend functions for one-time operations
    suspend fun getAllCategoriesSync(): List<Category> = categoryDao.getAllCategoriesSync()
    
    suspend fun getCategoryByIdSync(categoryId: Long): Category? = categoryDao.getCategoryById(categoryId)
    
    suspend fun getCategoryByName(name: String): Category? = categoryDao.getCategoryByName(name)
    
    suspend fun getDefaultCategories(): List<Category> = categoryDao.getDefaultCategories()
    
    suspend fun getCustomCategories(): List<Category> = categoryDao.getCustomCategories()
    
    suspend fun getCategoryCount(): Int = categoryDao.getCategoryCount()
    
    suspend fun insertCategory(category: Category): Long {
        return categoryDao.insertCategory(category.copy(updatedAt = System.currentTimeMillis()))
    }
    
    suspend fun insertCategories(categories: List<Category>): List<Long> {
        val timestamp = System.currentTimeMillis()
        val updatedCategories = categories.map { it.copy(updatedAt = timestamp) }
        return categoryDao.insertCategories(updatedCategories)
    }
    
    suspend fun updateCategory(category: Category) {
        categoryDao.updateCategory(category.copy(updatedAt = System.currentTimeMillis()))
    }
    
    suspend fun deleteCategory(category: Category) {
        categoryDao.deleteCategory(category)
    }
    
    suspend fun deleteCategoryById(categoryId: Long) {
        categoryDao.deleteCategoryById(categoryId)
    }
    
    suspend fun deleteAllCustomCategories() {
        categoryDao.deleteAllCustomCategories()
    }
    
    suspend fun initializeDefaultCategories() {
        val existingCount = getCategoryCount()
        if (existingCount == 0) {
            val defaultCategories = Category.getDefaultCategories()
            insertCategories(defaultCategories)
        }
    }
    
    suspend fun isCategoryNameExists(name: String, excludeId: Long? = null): Boolean {
        val existingCategory = getCategoryByName(name)
        return existingCategory != null && (excludeId == null || existingCategory.id != excludeId)
    }
    
    suspend fun createCategory(
        name: String,
        color: String,
        icon: String? = null,
        description: String? = null
    ): Result<Long> {
        return try {
            if (isCategoryNameExists(name)) {
                Result.failure(Exception("Category with name '$name' already exists"))
            } else {
                val category = Category(
                    name = name,
                    color = color,
                    icon = icon,
                    description = description,
                    isDefault = false
                )
                val id = insertCategory(category)
                Result.success(id)
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun updateCategory(
        categoryId: Long,
        name: String,
        color: String,
        icon: String? = null,
        description: String? = null
    ): Result<Unit> {
        return try {
            val existingCategory = getCategoryByIdSync(categoryId)
                ?: return Result.failure(Exception("Category not found"))
            
            if (isCategoryNameExists(name, categoryId)) {
                Result.failure(Exception("Category with name '$name' already exists"))
            } else {
                val updatedCategory = existingCategory.copy(
                    name = name,
                    color = color,
                    icon = icon,
                    description = description
                )
                updateCategory(updatedCategory)
                Result.success(Unit)
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}