#!/usr/bin/env python3
"""
Simple compilation test script to check if our Kotlin files have syntax errors
"""
import os
import sys
import subprocess

def check_kotlin_syntax():
    """Check if Kotlin files have basic syntax errors"""
    kotlin_files = []
    
    # Find all Kotlin files
    for root, dirs, files in os.walk("app/src/main/java"):
        for file in files:
            if file.endswith(".kt"):
                kotlin_files.append(os.path.join(root, file))
    
    print(f"🔍 Found {len(kotlin_files)} Kotlin files")
    
    # Basic syntax check (just try to read and parse basic structure)
    for kotlin_file in kotlin_files:
        try:
            with open(kotlin_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Basic checks
            if 'package ' not in content:
                print(f"⚠️  {kotlin_file} - Missing package declaration")
            
            # Check for unmatched braces (basic)
            open_braces = content.count('{')
            close_braces = content.count('}')
            if open_braces != close_braces:
                print(f"⚠️  {kotlin_file} - Unmatched braces: {open_braces} open, {close_braces} close")
            else:
                print(f"✅ {kotlin_file} - Basic syntax OK")
                
        except Exception as e:
            print(f"❌ {kotlin_file} - Error reading file: {e}")
    
    return True

def check_xml_syntax():
    """Check XML files for basic syntax"""
    import xml.etree.ElementTree as ET
    
    xml_files = []
    for root, dirs, files in os.walk("app/src/main/res"):
        for file in files:
            if file.endswith(".xml"):
                xml_files.append(os.path.join(root, file))
    
    print(f"🔍 Found {len(xml_files)} XML files")
    
    all_valid = True
    for xml_file in xml_files:
        try:
            ET.parse(xml_file)
            print(f"✅ {xml_file} - Valid XML")
        except ET.ParseError as e:
            print(f"❌ {xml_file} - XML Parse Error: {e}")
            all_valid = False
        except Exception as e:
            print(f"❌ {xml_file} - Error: {e}")
            all_valid = False
    
    return all_valid

def main():
    """Main test function"""
    print("🚀 BookKeeping2025 Compilation Test")
    print("=" * 50)
    
    print("\n📋 Checking Kotlin files...")
    kotlin_ok = check_kotlin_syntax()
    
    print("\n📋 Checking XML files...")
    xml_ok = check_xml_syntax()
    
    print("\n" + "=" * 50)
    if kotlin_ok and xml_ok:
        print("🎉 All basic syntax checks passed!")
        print("💡 The project should compile successfully")
        return 0
    else:
        print("💥 Some files have syntax errors!")
        print("🔧 Please fix the errors before building")
        return 1

if __name__ == "__main__":
    sys.exit(main())