package com.expensetracker.data.database;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0014\u0010\u0007\u001a\u0004\u0018\u00010\u00042\b\u0010\b\u001a\u0004\u0018\u00010\tH\u0007J\u0010\u0010\n\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0007J\u0014\u0010\u000b\u001a\u0004\u0018\u00010\t2\b\u0010\b\u001a\u0004\u0018\u00010\u0004H\u0007\u00a8\u0006\f"}, d2 = {"Lcom/expensetracker/data/database/Converters;", "", "()V", "fromPaymentMethod", "", "paymentMethod", "Lcom/expensetracker/data/entity/PaymentMethod;", "fromRecurringType", "recurringType", "Lcom/expensetracker/data/entity/RecurringType;", "toPaymentMethod", "toRecurringType", "app_debug"})
public final class Converters {
    
    public Converters() {
        super();
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String fromPaymentMethod(@org.jetbrains.annotations.NotNull()
    com.expensetracker.data.entity.PaymentMethod paymentMethod) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.NotNull()
    public final com.expensetracker.data.entity.PaymentMethod toPaymentMethod(@org.jetbrains.annotations.NotNull()
    java.lang.String paymentMethod) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String fromRecurringType(@org.jetbrains.annotations.Nullable()
    com.expensetracker.data.entity.RecurringType recurringType) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final com.expensetracker.data.entity.RecurringType toRecurringType(@org.jetbrains.annotations.Nullable()
    java.lang.String recurringType) {
        return null;
    }
}