<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_category" modulePackage="com.expensetracker" filePath="app\src\main\res\layout\item_category.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_category_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="92" endOffset="51"/></Target><Target id="@+id/view_category_color" view="View"><Expressions/><location startLine="17" startOffset="8" endLine="23" endOffset="59"/></Target><Target id="@+id/text_category_name" view="TextView"><Expressions/><location startLine="32" startOffset="12" endLine="38" endOffset="48"/></Target><Target id="@+id/text_expense_count" view="TextView"><Expressions/><location startLine="40" startOffset="12" endLine="47" endOffset="42"/></Target><Target id="@+id/text_total_amount" view="TextView"><Expressions/><location startLine="58" startOffset="12" endLine="65" endOffset="38"/></Target><Target id="@+id/button_more_options" view="ImageButton"><Expressions/><location startLine="79" startOffset="8" endLine="88" endOffset="52"/></Target></Targets></Layout>