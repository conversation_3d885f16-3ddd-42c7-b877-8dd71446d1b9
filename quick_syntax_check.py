#!/usr/bin/env python3
"""
快速語法檢查腳本 - 檢查關鍵檔案的基本語法
"""
import os
import xml.etree.ElementTree as ET

def check_key_files():
    """檢查關鍵檔案"""
    print("🔍 BookKeeping2025 快速語法檢查")
    print("=" * 50)
    
    # 檢查關鍵 Kotlin 檔案
    kotlin_files = [
        "app/src/main/java/com/expensetracker/MainActivity.kt",
        "app/src/main/java/com/expensetracker/ExpenseTrackerApplication.kt",
        "app/src/main/java/com/expensetracker/data/database/ExpenseDatabase.kt",
        "app/src/main/java/com/expensetracker/data/entity/Category.kt",
        "app/src/main/java/com/expensetracker/data/entity/Expense.kt",
    ]
    
    print("\n📱 檢查 Kotlin 檔案...")
    for file_path in kotlin_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 基本檢查
                if 'package ' in content:
                    print(f"✅ {file_path} - 語法基本正確")
                else:
                    print(f"⚠️  {file_path} - 缺少 package 聲明")
            except Exception as e:
                print(f"❌ {file_path} - 讀取錯誤: {e}")
        else:
            print(f"❌ {file_path} - 檔案不存在")
    
    # 檢查關鍵 XML 檔案
    xml_files = [
        "app/src/main/AndroidManifest.xml",
        "app/src/main/res/layout/activity_main.xml",
        "app/src/main/res/layout/fragment_home.xml",
        "app/src/main/res/values/strings.xml",
        "app/src/main/res/values/colors.xml",
        "app/src/main/res/values/themes.xml",
    ]
    
    print("\n🎨 檢查 XML 檔案...")
    xml_ok = True
    for file_path in xml_files:
        if os.path.exists(file_path):
            try:
                ET.parse(file_path)
                print(f"✅ {file_path} - XML 格式正確")
            except ET.ParseError as e:
                print(f"❌ {file_path} - XML 解析錯誤: {e}")
                xml_ok = False
            except Exception as e:
                print(f"❌ {file_path} - 錯誤: {e}")
                xml_ok = False
        else:
            print(f"❌ {file_path} - 檔案不存在")
            xml_ok = False
    
    # 檢查 build.gradle.kts
    print("\n🔧 檢查建置檔案...")
    build_files = [
        "build.gradle.kts",
        "app/build.gradle.kts",
        "gradle.properties",
        "settings.gradle.kts"
    ]
    
    for file_path in build_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} - 檔案存在")
        else:
            print(f"❌ {file_path} - 檔案不存在")
    
    print("\n" + "=" * 50)
    if xml_ok:
        print("🎉 基本語法檢查通過！")
        print("💡 專案應該可以編譯成功")
        
        print("\n📋 建議的測試步驟：")
        print("1. 開啟 Android Studio")
        print("2. File > Open > 選擇 BookKeeping2025 資料夾")
        print("3. 等待 Gradle 同步完成")
        print("4. Build > Make Project")
        print("5. 如果成功，嘗試 Run > Run 'app'")
        
        return True
    else:
        print("💥 發現語法錯誤！")
        print("🔧 請修復錯誤後再嘗試建置")
        return False

if __name__ == "__main__":
    check_key_files()