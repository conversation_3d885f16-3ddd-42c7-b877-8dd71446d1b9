package com.expensetracker.ui.adapter

import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.expensetracker.data.entity.ExpenseWithCategory
import com.expensetracker.databinding.ItemExpenseBinding

class ExpenseAdapter(
    private val onExpenseClick: (ExpenseWithCategory) -> Unit,
    private val onExpenseLongClick: (ExpenseWithCategory) -> Unit = {}
) : ListAdapter<ExpenseWithCategory, ExpenseAdapter.ExpenseViewHolder>(ExpenseDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ExpenseViewHolder {
        val binding = ItemExpenseBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ExpenseViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ExpenseViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class ExpenseViewHolder(
        private val binding: ItemExpenseBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(expenseWithCategory: ExpenseWithCategory) {
            val expense = expenseWithCategory.expense
            val category = expenseWithCategory.category

            // 設置費用描述
            binding.textExpenseDescription.text = expense.description

            // 設置類別名稱
            binding.textExpenseCategory.text = category.name

            // 設置日期
            binding.textExpenseDate.text = expense.getFormattedDateTime("MMM dd, HH:mm")

            // 設置金額
            binding.textExpenseAmount.text = expense.getFormattedAmount()

            // 設置類別顏色指示器
            try {
                val categoryColor = Color.parseColor(category.color)
                binding.viewCategoryColor.setBackgroundColor(categoryColor)
            } catch (e: IllegalArgumentException) {
                // 如果顏色格式錯誤，使用預設顏色
                binding.viewCategoryColor.setBackgroundColor(Color.parseColor("#4CAF50"))
            }

            // 設置點擊事件
            binding.root.setOnClickListener {
                onExpenseClick(expenseWithCategory)
            }

            binding.root.setOnLongClickListener {
                onExpenseLongClick(expenseWithCategory)
                true
            }
        }
    }

    private class ExpenseDiffCallback : DiffUtil.ItemCallback<ExpenseWithCategory>() {
        override fun areItemsTheSame(
            oldItem: ExpenseWithCategory,
            newItem: ExpenseWithCategory
        ): Boolean {
            return oldItem.expense.id == newItem.expense.id
        }

        override fun areContentsTheSame(
            oldItem: ExpenseWithCategory,
            newItem: ExpenseWithCategory
        ): Boolean {
            return oldItem.expense == newItem.expense && oldItem.category == newItem.category
        }
    }
}