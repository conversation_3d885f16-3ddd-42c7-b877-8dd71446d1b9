package com.expensetracker.data.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.expensetracker.data.entity.Expense
import com.expensetracker.data.entity.ExpenseWithCategory
import com.expensetracker.data.entity.PaymentMethod

@Dao
interface ExpenseDao {
    
    @Query("SELECT * FROM expenses ORDER BY date DESC")
    fun getAllExpenses(): LiveData<List<Expense>>
    
    @Query("SELECT * FROM expenses ORDER BY date DESC")
    suspend fun getAllExpensesSync(): List<Expense>
    
    @Transaction
    @Query("SELECT * FROM expenses ORDER BY date DESC")
    fun getAllExpensesWithCategory(): LiveData<List<ExpenseWithCategory>>
    
    @Transaction
    @Query("SELECT * FROM expenses ORDER BY date DESC LIMIT :limit")
    fun getRecentExpensesWithCategory(limit: Int = 10): LiveData<List<ExpenseWithCategory>>
    
    @Query("SELECT * FROM expenses WHERE id = :expenseId")
    suspend fun getExpenseById(expenseId: Long): Expense?
    
    @Transaction
    @Query("SELECT * FROM expenses WHERE id = :expenseId")
    suspend fun getExpenseWithCategoryById(expenseId: Long): ExpenseWithCategory?
    
    @Query("SELECT * FROM expenses WHERE category_id = :categoryId ORDER BY date DESC")
    fun getExpensesByCategory(categoryId: Long): LiveData<List<Expense>>
    
    @Transaction
    @Query("SELECT * FROM expenses WHERE category_id = :categoryId ORDER BY date DESC")
    fun getExpensesWithCategoryByCategory(categoryId: Long): LiveData<List<ExpenseWithCategory>>
    
    @Query("SELECT * FROM expenses WHERE date BETWEEN :startDate AND :endDate ORDER BY date DESC")
    fun getExpensesByDateRange(startDate: Long, endDate: Long): LiveData<List<Expense>>
    
    @Transaction
    @Query("SELECT * FROM expenses WHERE date BETWEEN :startDate AND :endDate ORDER BY date DESC")
    fun getExpensesWithCategoryByDateRange(startDate: Long, endDate: Long): LiveData<List<ExpenseWithCategory>>
    
    @Query("SELECT * FROM expenses WHERE amount BETWEEN :minAmount AND :maxAmount ORDER BY date DESC")
    fun getExpensesByAmountRange(minAmount: Double, maxAmount: Double): LiveData<List<Expense>>
    
    @Query("SELECT * FROM expenses WHERE payment_method = :paymentMethod ORDER BY date DESC")
    fun getExpensesByPaymentMethod(paymentMethod: PaymentMethod): LiveData<List<Expense>>
    
    @Query("SELECT * FROM expenses WHERE description LIKE '%' || :searchQuery || '%' OR notes LIKE '%' || :searchQuery || '%' ORDER BY date DESC")
    fun searchExpenses(searchQuery: String): LiveData<List<Expense>>
    
    @Transaction
    @Query("SELECT * FROM expenses WHERE description LIKE '%' || :searchQuery || '%' OR notes LIKE '%' || :searchQuery || '%' ORDER BY date DESC")
    fun searchExpensesWithCategory(searchQuery: String): LiveData<List<ExpenseWithCategory>>
    
    // Statistics queries
    @Query("SELECT SUM(amount) FROM expenses")
    suspend fun getTotalExpenses(): Double?
    
    @Query("SELECT SUM(amount) FROM expenses")
    fun getTotalExpensesLiveData(): LiveData<Double?>
    
    @Query("SELECT SUM(amount) FROM expenses WHERE date BETWEEN :startDate AND :endDate")
    suspend fun getTotalExpensesByDateRange(startDate: Long, endDate: Long): Double?
    
    @Query("SELECT SUM(amount) FROM expenses WHERE date BETWEEN :startDate AND :endDate")
    fun getTotalExpensesByDateRangeLiveData(startDate: Long, endDate: Long): LiveData<Double?>
    
    @Query("SELECT SUM(amount) FROM expenses WHERE category_id = :categoryId")
    suspend fun getTotalExpensesByCategory(categoryId: Long): Double?
    
    @Query("SELECT COUNT(*) FROM expenses")
    suspend fun getExpenseCount(): Int
    
    @Query("SELECT COUNT(*) FROM expenses WHERE date BETWEEN :startDate AND :endDate")
    suspend fun getExpenseCountByDateRange(startDate: Long, endDate: Long): Int
    
    @Query("SELECT AVG(amount) FROM expenses")
    suspend fun getAverageExpenseAmount(): Double?
    
    @Query("SELECT MAX(amount) FROM expenses")
    suspend fun getMaxExpenseAmount(): Double?
    
    @Query("SELECT MIN(amount) FROM expenses")
    suspend fun getMinExpenseAmount(): Double?
    
    // Monthly statistics
    @Query("""
        SELECT 
            strftime('%Y-%m', datetime(date/1000, 'unixepoch')) as month,
            SUM(amount) as total_amount,
            COUNT(*) as expense_count
        FROM expenses 
        GROUP BY strftime('%Y-%m', datetime(date/1000, 'unixepoch'))
        ORDER BY month DESC
        LIMIT :limit
    """)
    suspend fun getMonthlyExpenseSummary(limit: Int = 12): List<MonthlyExpenseSummary>
    
    // Category statistics
    @Query("""
        SELECT 
            c.name as category_name,
            c.color as category_color,
            SUM(e.amount) as total_amount,
            COUNT(e.id) as expense_count
        FROM expenses e
        INNER JOIN categories c ON e.category_id = c.id
        GROUP BY e.category_id, c.name, c.color
        ORDER BY total_amount DESC
    """)
    suspend fun getCategoryExpenseSummary(): List<CategoryExpenseSummary>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertExpense(expense: Expense): Long
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertExpenses(expenses: List<Expense>): List<Long>
    
    @Update
    suspend fun updateExpense(expense: Expense)
    
    @Delete
    suspend fun deleteExpense(expense: Expense)
    
    @Query("DELETE FROM expenses WHERE id = :expenseId")
    suspend fun deleteExpenseById(expenseId: Long)
    
    @Query("DELETE FROM expenses WHERE category_id = :categoryId")
    suspend fun deleteExpensesByCategory(categoryId: Long)
    
    @Query("DELETE FROM expenses")
    suspend fun deleteAllExpenses()
    
    @Query("UPDATE expenses SET updated_at = :timestamp WHERE id = :expenseId")
    suspend fun updateExpenseTimestamp(expenseId: Long, timestamp: Long = System.currentTimeMillis())
}

// Data classes for statistics
data class MonthlyExpenseSummary(
    val month: String,
    @ColumnInfo(name = "total_amount") val totalAmount: Double,
    @ColumnInfo(name = "expense_count") val expenseCount: Int
)

data class CategoryExpenseSummary(
    @ColumnInfo(name = "category_name") val categoryName: String,
    @ColumnInfo(name = "category_color") val categoryColor: String,
    @ColumnInfo(name = "total_amount") val totalAmount: Double,
    @ColumnInfo(name = "expense_count") val expenseCount: Int
)