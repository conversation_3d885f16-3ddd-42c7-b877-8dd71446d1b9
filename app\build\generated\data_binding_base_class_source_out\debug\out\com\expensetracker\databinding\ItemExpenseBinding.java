// Generated by view binder compiler. Do not edit!
package com.expensetracker.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.expensetracker.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemExpenseBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView textExpenseAmount;

  @NonNull
  public final TextView textExpenseCategory;

  @NonNull
  public final TextView textExpenseDate;

  @NonNull
  public final TextView textExpenseDescription;

  @NonNull
  public final View viewCategoryColor;

  private ItemExpenseBinding(@NonNull MaterialCardView rootView,
      @NonNull TextView textExpenseAmount, @NonNull TextView textExpenseCategory,
      @NonNull TextView textExpenseDate, @NonNull TextView textExpenseDescription,
      @NonNull View viewCategoryColor) {
    this.rootView = rootView;
    this.textExpenseAmount = textExpenseAmount;
    this.textExpenseCategory = textExpenseCategory;
    this.textExpenseDate = textExpenseDate;
    this.textExpenseDescription = textExpenseDescription;
    this.viewCategoryColor = viewCategoryColor;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemExpenseBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemExpenseBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_expense, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemExpenseBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.text_expense_amount;
      TextView textExpenseAmount = ViewBindings.findChildViewById(rootView, id);
      if (textExpenseAmount == null) {
        break missingId;
      }

      id = R.id.text_expense_category;
      TextView textExpenseCategory = ViewBindings.findChildViewById(rootView, id);
      if (textExpenseCategory == null) {
        break missingId;
      }

      id = R.id.text_expense_date;
      TextView textExpenseDate = ViewBindings.findChildViewById(rootView, id);
      if (textExpenseDate == null) {
        break missingId;
      }

      id = R.id.text_expense_description;
      TextView textExpenseDescription = ViewBindings.findChildViewById(rootView, id);
      if (textExpenseDescription == null) {
        break missingId;
      }

      id = R.id.view_category_color;
      View viewCategoryColor = ViewBindings.findChildViewById(rootView, id);
      if (viewCategoryColor == null) {
        break missingId;
      }

      return new ItemExpenseBinding((MaterialCardView) rootView, textExpenseAmount,
          textExpenseCategory, textExpenseDate, textExpenseDescription, viewCategoryColor);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
