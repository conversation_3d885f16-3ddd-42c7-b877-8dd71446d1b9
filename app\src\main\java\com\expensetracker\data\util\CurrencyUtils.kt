package com.expensetracker.data.util

import java.text.NumberFormat
import java.util.*

object CurrencyUtils {
    
    private val currencyFormat = NumberFormat.getCurrencyInstance(Locale.getDefault())
    
    fun formatAmount(amount: Double, currencySymbol: String = "$"): String {
        return String.format("$currencySymbol%.2f", amount)
    }
    
    fun formatAmountWithLocale(amount: Double): String {
        return currencyFormat.format(amount)
    }
    
    fun parseAmount(amountString: String): Double? {
        return try {
            // Remove currency symbols and parse
            val cleanString = amountString.replace(Regex("[^\\d.-]"), "")
            cleanString.toDoubleOrNull()
        } catch (e: Exception) {
            null
        }
    }
    
    fun isValidAmount(amount: Double): Boolean {
        return amount > 0 && amount.isFinite()
    }
    
    fun roundToTwoDecimals(amount: Double): Double {
        return Math.round(amount * 100.0) / 100.0
    }
}