// Generated by view binder compiler. Do not edit!
package com.expensetracker.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.expensetracker.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemCategoryBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageButton buttonMoreOptions;

  @NonNull
  public final TextView textCategoryName;

  @NonNull
  public final TextView textExpenseCount;

  @NonNull
  public final TextView textTotalAmount;

  @NonNull
  public final View viewCategoryColor;

  private ItemCategoryBinding(@NonNull MaterialCardView rootView,
      @NonNull ImageButton buttonMoreOptions, @NonNull TextView textCategoryName,
      @NonNull TextView textExpenseCount, @NonNull TextView textTotalAmount,
      @NonNull View viewCategoryColor) {
    this.rootView = rootView;
    this.buttonMoreOptions = buttonMoreOptions;
    this.textCategoryName = textCategoryName;
    this.textExpenseCount = textExpenseCount;
    this.textTotalAmount = textTotalAmount;
    this.viewCategoryColor = viewCategoryColor;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemCategoryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemCategoryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_category, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemCategoryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_more_options;
      ImageButton buttonMoreOptions = ViewBindings.findChildViewById(rootView, id);
      if (buttonMoreOptions == null) {
        break missingId;
      }

      id = R.id.text_category_name;
      TextView textCategoryName = ViewBindings.findChildViewById(rootView, id);
      if (textCategoryName == null) {
        break missingId;
      }

      id = R.id.text_expense_count;
      TextView textExpenseCount = ViewBindings.findChildViewById(rootView, id);
      if (textExpenseCount == null) {
        break missingId;
      }

      id = R.id.text_total_amount;
      TextView textTotalAmount = ViewBindings.findChildViewById(rootView, id);
      if (textTotalAmount == null) {
        break missingId;
      }

      id = R.id.view_category_color;
      View viewCategoryColor = ViewBindings.findChildViewById(rootView, id);
      if (viewCategoryColor == null) {
        break missingId;
      }

      return new ItemCategoryBinding((MaterialCardView) rootView, buttonMoreOptions,
          textCategoryName, textExpenseCount, textTotalAmount, viewCategoryColor);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
