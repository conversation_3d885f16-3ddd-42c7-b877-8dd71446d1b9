package com.expensetracker.ui.expenses

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.expensetracker.data.entity.Category
import com.expensetracker.data.entity.PaymentMethod
import com.expensetracker.data.repository.CategoryRepository
import com.expensetracker.data.repository.ExpenseRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class AddExpenseViewModel @Inject constructor(
    private val expenseRepository: ExpenseRepository,
    private val categoryRepository: CategoryRepository
) : ViewModel() {

    // Categories for dropdown
    val categories: LiveData<List<Category>> = categoryRepository.getAllCategories()

    // Form state
    private val _isLoading = MutableLiveData<Boolean>().apply { value = false }
    val isLoading: LiveData<Boolean> = _isLoading

    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage

    private val _successMessage = MutableLiveData<String?>()
    val successMessage: LiveData<String?> = _successMessage

    private val _navigateBack = MutableLiveData<Boolean>().apply { value = false }
    val navigateBack: LiveData<Boolean> = _navigateBack

    // Form validation
    private val _amountError = MutableLiveData<String?>()
    val amountError: LiveData<String?> = _amountError

    private val _descriptionError = MutableLiveData<String?>()
    val descriptionError: LiveData<String?> = _descriptionError

    private val _categoryError = MutableLiveData<String?>()
    val categoryError: LiveData<String?> = _categoryError

    // Payment methods for dropdown
    val paymentMethods = PaymentMethod.values().map { method ->
        when (method) {
            PaymentMethod.CASH -> "Cash"
            PaymentMethod.CREDIT_CARD -> "Credit Card"
            PaymentMethod.DEBIT_CARD -> "Debit Card"
            PaymentMethod.BANK_TRANSFER -> "Bank Transfer"
            PaymentMethod.DIGITAL_WALLET -> "Digital Wallet"
            PaymentMethod.CHECK -> "Check"
            PaymentMethod.OTHER -> "Other"
        }
    }

    fun createExpense(
        amount: String,
        description: String,
        categoryId: Long?,
        date: Long,
        paymentMethod: PaymentMethod,
        notes: String?,
        location: String?
    ) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                clearErrors()

                // Validate input
                if (!validateInput(amount, description, categoryId)) {
                    _isLoading.value = false
                    return@launch
                }

                val amountDouble = amount.toDoubleOrNull() ?: 0.0
                val result = expenseRepository.createExpense(
                    amount = amountDouble,
                    description = description.trim(),
                    categoryId = categoryId!!,
                    date = date,
                    notes = notes?.trim()?.takeIf { it.isNotEmpty() },
                    location = location?.trim()?.takeIf { it.isNotEmpty() },
                    paymentMethod = paymentMethod
                )

                result.fold(
                    onSuccess = {
                        _successMessage.value = "Expense created successfully"
                        _navigateBack.value = true
                    },
                    onFailure = { exception ->
                        _errorMessage.value = exception.message ?: "Error creating expense"
                    }
                )

            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Unknown error occurred"
            } finally {
                _isLoading.value = false
            }
        }
    }

    private fun validateInput(amount: String, description: String, categoryId: Long?): Boolean {
        var isValid = true

        // Validate amount
        val amountDouble = amount.toDoubleOrNull()
        if (amount.isBlank()) {
            _amountError.value = "Please enter an amount"
            isValid = false
        } else if (amountDouble == null || amountDouble <= 0) {
            _amountError.value = "Please enter a valid amount"
            isValid = false
        }

        // Validate description
        if (description.isBlank()) {
            _descriptionError.value = "Please enter a description"
            isValid = false
        } else if (description.trim().length < 2) {
            _descriptionError.value = "Description must be at least 2 characters"
            isValid = false
        }

        // Validate category
        if (categoryId == null) {
            _categoryError.value = "Please select a category"
            isValid = false
        }

        return isValid
    }

    fun getPaymentMethodFromDisplayName(displayName: String): PaymentMethod {
        return when (displayName) {
            "Cash" -> PaymentMethod.CASH
            "Credit Card" -> PaymentMethod.CREDIT_CARD
            "Debit Card" -> PaymentMethod.DEBIT_CARD
            "Bank Transfer" -> PaymentMethod.BANK_TRANSFER
            "Digital Wallet" -> PaymentMethod.DIGITAL_WALLET
            "Check" -> PaymentMethod.CHECK
            "Other" -> PaymentMethod.OTHER
            else -> PaymentMethod.CASH
        }
    }

    fun clearErrors() {
        _amountError.value = null
        _descriptionError.value = null
        _categoryError.value = null
        _errorMessage.value = null
    }

    fun clearMessages() {
        _errorMessage.value = null
        _successMessage.value = null
    }

    fun onNavigateBackComplete() {
        _navigateBack.value = false
    }
}