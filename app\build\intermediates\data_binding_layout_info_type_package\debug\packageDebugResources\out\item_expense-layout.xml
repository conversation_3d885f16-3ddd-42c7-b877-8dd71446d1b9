<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_expense" modulePackage="com.expensetracker" filePath="app\src\main\res\layout\item_expense.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_expense_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="71" endOffset="51"/></Target><Target id="@+id/view_category_color" view="View"><Expressions/><location startLine="17" startOffset="8" endLine="22" endOffset="55"/></Target><Target id="@+id/text_expense_description" view="TextView"><Expressions/><location startLine="31" startOffset="12" endLine="37" endOffset="47"/></Target><Target id="@+id/text_expense_category" view="TextView"><Expressions/><location startLine="39" startOffset="12" endLine="46" endOffset="48"/></Target><Target id="@+id/text_expense_date" view="TextView"><Expressions/><location startLine="48" startOffset="12" endLine="55" endOffset="45"/></Target><Target id="@+id/text_expense_amount" view="TextView"><Expressions/><location startLine="60" startOffset="8" endLine="67" endOffset="33"/></Target></Targets></Layout>