// Generated by view binder compiler. Do not edit!
package com.expensetracker.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.expensetracker.R;
import com.github.mikephil.charting.charts.LineChart;
import com.github.mikephil.charting.charts.PieChart;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentReportsBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final MaterialButton buttonExportCsv;

  @NonNull
  public final MaterialButton buttonExportPdf;

  @NonNull
  public final PieChart chartCategoryBreakdown;

  @NonNull
  public final LineChart chartMonthlyTrend;

  @NonNull
  public final TextView textAverageExpenses;

  @NonNull
  public final TextView textMonthlyExpenses;

  @NonNull
  public final TextView textTotalExpenses;

  private FragmentReportsBinding(@NonNull ScrollView rootView,
      @NonNull MaterialButton buttonExportCsv, @NonNull MaterialButton buttonExportPdf,
      @NonNull PieChart chartCategoryBreakdown, @NonNull LineChart chartMonthlyTrend,
      @NonNull TextView textAverageExpenses, @NonNull TextView textMonthlyExpenses,
      @NonNull TextView textTotalExpenses) {
    this.rootView = rootView;
    this.buttonExportCsv = buttonExportCsv;
    this.buttonExportPdf = buttonExportPdf;
    this.chartCategoryBreakdown = chartCategoryBreakdown;
    this.chartMonthlyTrend = chartMonthlyTrend;
    this.textAverageExpenses = textAverageExpenses;
    this.textMonthlyExpenses = textMonthlyExpenses;
    this.textTotalExpenses = textTotalExpenses;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentReportsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentReportsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_reports, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentReportsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_export_csv;
      MaterialButton buttonExportCsv = ViewBindings.findChildViewById(rootView, id);
      if (buttonExportCsv == null) {
        break missingId;
      }

      id = R.id.button_export_pdf;
      MaterialButton buttonExportPdf = ViewBindings.findChildViewById(rootView, id);
      if (buttonExportPdf == null) {
        break missingId;
      }

      id = R.id.chart_category_breakdown;
      PieChart chartCategoryBreakdown = ViewBindings.findChildViewById(rootView, id);
      if (chartCategoryBreakdown == null) {
        break missingId;
      }

      id = R.id.chart_monthly_trend;
      LineChart chartMonthlyTrend = ViewBindings.findChildViewById(rootView, id);
      if (chartMonthlyTrend == null) {
        break missingId;
      }

      id = R.id.text_average_expenses;
      TextView textAverageExpenses = ViewBindings.findChildViewById(rootView, id);
      if (textAverageExpenses == null) {
        break missingId;
      }

      id = R.id.text_monthly_expenses;
      TextView textMonthlyExpenses = ViewBindings.findChildViewById(rootView, id);
      if (textMonthlyExpenses == null) {
        break missingId;
      }

      id = R.id.text_total_expenses;
      TextView textTotalExpenses = ViewBindings.findChildViewById(rootView, id);
      if (textTotalExpenses == null) {
        break missingId;
      }

      return new FragmentReportsBinding((ScrollView) rootView, buttonExportCsv, buttonExportPdf,
          chartCategoryBreakdown, chartMonthlyTrend, textAverageExpenses, textMonthlyExpenses,
          textTotalExpenses);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
