#!/usr/bin/env python3
"""
快速測試腳本 - 檢查模擬器狀態並提供測試指導
"""
import subprocess
import sys
import os
import time

def run_command(command, capture_output=True):
    """執行命令並返回結果"""
    try:
        if capture_output:
            result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=30)
            return result.returncode == 0, result.stdout, result.stderr
        else:
            result = subprocess.run(command, shell=True, timeout=30)
            return result.returncode == 0, "", ""
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)

def check_adb():
    """檢查ADB是否可用"""
    print("🔍 檢查 ADB 工具...")
    success, stdout, stderr = run_command("adb version")
    if success:
        print("✅ ADB 工具可用")
        return True
    else:
        print("❌ ADB 工具不可用")
        print("💡 請確認 Android SDK 已安裝並加入 PATH")
        return False

def check_emulator():
    """檢查模擬器狀態"""
    print("🔍 檢查模擬器狀態...")
    success, stdout, stderr = run_command("adb devices")
    if success:
        lines = stdout.strip().split('\n')
        devices = [line for line in lines if 'device' in line and 'List of devices' not in line]
        
        if devices:
            print(f"✅ 找到 {len(devices)} 個裝置/模擬器:")
            for device in devices:
                print(f"   📱 {device}")
            return True
        else:
            print("⚠️  沒有找到運行中的模擬器")
            print("💡 請啟動 Android 模擬器")
            return False
    else:
        print("❌ 無法檢查模擬器狀態")
        return False

def check_apk():
    """檢查APK檔案是否存在"""
    print("🔍 檢查 APK 檔案...")
    apk_path = "app/build/outputs/apk/debug/app-debug.apk"
    if os.path.exists(apk_path):
        print("✅ 找到 APK 檔案")
        return True
    else:
        print("❌ 找不到 APK 檔案")
        print("💡 請先建置專案")
        return False

def install_app():
    """安裝應用程式到模擬器"""
    print("🔧 安裝應用程式到模擬器...")
    apk_path = "app/build/outputs/apk/debug/app-debug.apk"
    success, stdout, stderr = run_command(f"adb install -r {apk_path}")
    if success:
        print("✅ 應用程式安裝成功")
        return True
    else:
        print("❌ 應用程式安裝失敗")
        print(f"錯誤訊息: {stderr}")
        return False

def launch_app():
    """啟動應用程式"""
    print("🚀 啟動應用程式...")
    success, stdout, stderr = run_command("adb shell am start -n com.expensetracker/.MainActivity")
    if success:
        print("✅ 應用程式啟動成功")
        return True
    else:
        print("❌ 應用程式啟動失敗")
        print(f"錯誤訊息: {stderr}")
        return False

def show_test_guide():
    """顯示測試指南"""
    print("\n" + "="*60)
    print("📋 新增記帳功能測試指南")
    print("="*60)
    print()
    print("🎯 測試步驟：")
    print("1. 📱 確認應用程式已在模擬器中開啟")
    print("2. 🏠 檢查主畫面是否正常顯示")
    print("3. 📊 點擊底部導航的 'Expenses' 分頁")
    print("4. ➕ 點擊右下角的 FAB 新增按鈕")
    print("5. 📝 填寫新增記帳表單")
    print("6. 💾 點擊 'Save' 按鈕測試儲存功能")
    print()
    print("🧪 重點測試項目：")
    print("✅ 表單驗證：嘗試提交空白或無效資料")
    print("✅ 日期選擇：測試日期選擇器功能")
    print("✅ 下拉選單：測試分類和付款方式選擇")
    print("✅ 取消功能：測試取消按鈕")
    print("✅ 成功回饋：確認成功新增後的訊息")
    print("✅ 資料顯示：確認新增的記錄出現在列表中")
    print()
    print("📋 測試表單範例：")
    print("   金額: 25.99")
    print("   描述: 午餐 - 麥當勞")
    print("   分類: Food & Dining")
    print("   日期: 今天")
    print("   付款方式: Cash")
    print("   備註: 很好吃 (選填)")
    print("   地點: 台北市 (選填)")
    print()

def main():
    """主要測試流程"""
    print("📱 BookKeeping2025 快速測試工具")
    print("="*50)
    print()
    
    # 檢查環境
    if not check_adb():
        return 1
    
    emulator_ready = check_emulator()
    apk_exists = check_apk()
    
    if not emulator_ready:
        print("\n💡 請先啟動 Android 模擬器：")
        print("   1. 開啟 Android Studio")
        print("   2. 點擊 Device Manager")
        print("   3. 啟動 Medium_Phone_API_36 模擬器")
        print("   4. 等待模擬器完全啟動")
        print("   5. 重新執行此腳本")
        return 1
    
    if not apk_exists:
        print("\n💡 請先建置專案：")
        print("   1. 開啟 Android Studio")
        print("   2. 開啟 BookKeeping2025 專案")
        print("   3. 點擊 Build > Make Project")
        print("   4. 等待建置完成")
        print("   5. 重新執行此腳本")
        return 1
    
    # 安裝和啟動應用程式
    print("\n🚀 開始安裝和測試...")
    
    if install_app():
        time.sleep(2)  # 等待安裝完成
        if launch_app():
            time.sleep(3)  # 等待應用程式啟動
            show_test_guide()
            
            print("\n🎉 測試準備完成！")
            print("現在可以在模擬器中測試新增記帳功能了。")
            return 0
        else:
            print("\n❌ 應用程式啟動失敗")
            return 1
    else:
        print("\n❌ 應用程式安裝失敗")
        return 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n⚠️  測試被使用者中斷")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 發生錯誤: {e}")
        sys.exit(1)