,com.expensetracker.ExpenseTrackerApplicationcom.expensetracker.MainActivity0com.expensetracker.data.database.ExpenseDatabaseKcom.expensetracker.data.database.ExpenseDatabase.Companion.DatabaseCallback'com.expensetracker.data.entity.Category&com.expensetracker.data.entity.Expense,com.expensetracker.data.entity.PaymentMethod,<EMAIL>,com.expensetracker.ui.adapter.ExpenseAdapter><EMAIL>/com.expensetracker.ui.expenses.ExpensesFragment0com.expensetracker.ui.expenses.ExpensesViewModel'com.expensetracker.ui.home.HomeFragment(com.expensetracker.ui.home.HomeViewModel-com.expensetracker.ui.reports.ReportsFragment.com.expensetracker.ui.reports.ReportsViewModel2com.expensetracker.databinding.ItemCategoryBinding1com.expensetracker.databinding.ItemExpenseBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    