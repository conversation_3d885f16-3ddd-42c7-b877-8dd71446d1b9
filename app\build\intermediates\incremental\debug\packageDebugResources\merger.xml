<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\BookKeeping2025\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\BookKeeping2025\app\src\main\res"><file name="circle_background" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\drawable\circle_background.xml" qualifiers="" type="drawable"/><file name="ic_add_24" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\drawable\ic_add_24.xml" qualifiers="" type="drawable"/><file name="ic_analytics_24" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\drawable\ic_analytics_24.xml" qualifiers="" type="drawable"/><file name="ic_attach_money_24" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\drawable\ic_attach_money_24.xml" qualifiers="" type="drawable"/><file name="ic_calendar_24" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\drawable\ic_calendar_24.xml" qualifiers="" type="drawable"/><file name="ic_category_24" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\drawable\ic_category_24.xml" qualifiers="" type="drawable"/><file name="ic_close_24" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\drawable\ic_close_24.xml" qualifiers="" type="drawable"/><file name="ic_csv_24" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\drawable\ic_csv_24.xml" qualifiers="" type="drawable"/><file name="ic_description_24" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\drawable\ic_description_24.xml" qualifiers="" type="drawable"/><file name="ic_filter_24" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\drawable\ic_filter_24.xml" qualifiers="" type="drawable"/><file name="ic_home_24" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\drawable\ic_home_24.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_list_24" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\drawable\ic_list_24.xml" qualifiers="" type="drawable"/><file name="ic_location_24" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\drawable\ic_location_24.xml" qualifiers="" type="drawable"/><file name="ic_more_vert_24" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\drawable\ic_more_vert_24.xml" qualifiers="" type="drawable"/><file name="ic_note_24" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\drawable\ic_note_24.xml" qualifiers="" type="drawable"/><file name="ic_payment_24" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\drawable\ic_payment_24.xml" qualifiers="" type="drawable"/><file name="ic_pdf_24" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\drawable\ic_pdf_24.xml" qualifiers="" type="drawable"/><file name="ic_receipt_24" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\drawable\ic_receipt_24.xml" qualifiers="" type="drawable"/><file name="ic_save_24" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\drawable\ic_save_24.xml" qualifiers="" type="drawable"/><file name="ic_search_24" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\drawable\ic_search_24.xml" qualifiers="" type="drawable"/><file name="ic_sort_24" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\drawable\ic_sort_24.xml" qualifiers="" type="drawable"/><file name="activity_main" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="fragment_add_expense" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\layout\fragment_add_expense.xml" qualifiers="" type="layout"/><file name="fragment_categories" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\layout\fragment_categories.xml" qualifiers="" type="layout"/><file name="fragment_expenses" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\layout\fragment_expenses.xml" qualifiers="" type="layout"/><file name="fragment_home" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\layout\fragment_home.xml" qualifiers="" type="layout"/><file name="fragment_reports" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\layout\fragment_reports.xml" qualifiers="" type="layout"/><file name="item_category" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\layout\item_category.xml" qualifiers="" type="layout"/><file name="item_expense" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\layout\item_expense.xml" qualifiers="" type="layout"/><file name="bottom_nav_menu" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\menu\bottom_nav_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\mipmap-hdpi\ic_launcher_round.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\mipmap-mdpi\ic_launcher_round.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\mipmap-xhdpi\ic_launcher_round.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="mobile_navigation" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\navigation\mobile_navigation.xml" qualifiers="" type="navigation"/><file path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="primary_green">#FF4CAF50</color><color name="primary_green_dark">#FF388E3C</color><color name="accent_orange">#FFFF9800</color><color name="accent_orange_dark">#FFF57C00</color><color name="category_food">#FFFF5722</color><color name="category_transport">#FF2196F3</color><color name="category_entertainment">#FF9C27B0</color><color name="category_shopping">#FFFF9800</color><color name="category_health">#FF4CAF50</color><color name="category_utilities">#FF607D8B</color><color name="category_education">#FF3F51B5</color><color name="category_other">#FF795548</color><color name="success_green">#FF4CAF50</color><color name="error_red">#FFF44336</color><color name="warning_yellow">#FFFFC107</color><color name="info_blue">#FF2196F3</color><color name="background_light">#FFFAFAFA</color><color name="background_dark">#FF121212</color><color name="surface_light">#FFFFFFFF</color><color name="surface_dark">#FF1E1E1E</color></file><file path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">BookKeeping 2025</string><string name="action_settings">Settings</string><string name="title_home">Home</string><string name="title_expenses">Expenses</string><string name="title_categories">Categories</string><string name="title_reports">Reports</string><string name="add">Add</string><string name="edit">Edit</string><string name="delete">Delete</string><string name="save">Save</string><string name="cancel">Cancel</string><string name="confirm">Confirm</string><string name="expense_amount">Amount</string><string name="expense_description">Description</string><string name="expense_category">Category</string><string name="expense_date">Date</string><string name="add_expense">Add Expense</string><string name="edit_expense">Edit Expense</string><string name="delete_expense">Delete Expense</string><string name="category_name">Category Name</string><string name="category_color">Color</string><string name="add_category">Add Category</string><string name="edit_category">Edit Category</string><string name="delete_category">Delete Category</string><string name="total_expenses">Total Expenses</string><string name="monthly_report">Monthly Report</string><string name="yearly_report">Yearly Report</string><string name="category_breakdown">Category Breakdown</string><string name="expense_added">Expense added successfully</string><string name="expense_updated">Expense updated successfully</string><string name="expense_deleted">Expense deleted successfully</string><string name="category_added">Category added successfully</string><string name="category_updated">Category updated successfully</string><string name="category_deleted">Category deleted successfully</string><string name="error_empty_amount">Please enter an amount</string><string name="error_empty_description">Please enter a description</string><string name="error_empty_category_name">Please enter a category name</string><string name="error_invalid_amount">Please enter a valid amount</string><string name="welcome_title">Welcome to BookKeeping 2025</string><string name="welcome_subtitle">Track your expenses and manage your finances</string><string name="total_expenses_label">Total Expenses</string><string name="this_month_label">This Month</string><string name="quick_actions">Quick Actions</string><string name="recent_expenses">Recent Expenses</string><string name="view_all">View All</string><string name="no_expenses_message">No expenses yet. Add your first expense!</string><string name="add_expense_subtitle">Record your expense details</string><string name="expense_notes">Notes</string><string name="expense_location">Location</string><string name="payment_method">Payment Method</string><string name="optional_details">Optional Details</string><string name="saving">Saving...</string><string name="payment_cash">Cash</string><string name="payment_credit_card">Credit Card</string><string name="payment_debit_card">Debit Card</string><string name="payment_bank_transfer">Bank Transfer</string><string name="payment_digital_wallet">Digital Wallet</string><string name="payment_check">Check</string><string name="payment_other">Other</string><string name="error_amount_required">Please enter an amount</string><string name="error_amount_invalid">Please enter a valid amount</string><string name="error_description_required">Please enter a description</string><string name="error_description_too_short">Description must be at least 2 characters</string><string name="error_category_required">Please select a category</string><string name="expense_created_success">Expense created successfully</string><string name="expense_creation_error">Error creating expense</string></file><file path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.BookKeeping2025" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/primary_green</item>
        <item name="colorPrimaryVariant">@color/primary_green_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/accent_orange</item>
        <item name="colorSecondaryVariant">@color/accent_orange_dark</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style><style name="Theme.BookKeeping2025.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style><style name="Theme.BookKeeping2025.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/><style name="Theme.BookKeeping2025.PopupOverlay" parent="ThemeOverlay.AppCompat.Light"/><style name="AppButton" parent="Widget.Material3.Button">
        <item name="android:layout_height">48dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textAllCaps">false</item>
    </style><style name="AppButton.Primary" parent="AppButton">
        <item name="backgroundTint">@color/primary_green</item>
        <item name="android:textColor">@color/white</item>
    </style><style name="AppButton.Secondary" parent="AppButton">
        <item name="backgroundTint">@color/accent_orange</item>
        <item name="android:textColor">@color/white</item>
    </style><style name="AppTextInputLayout" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary_green</item>
        <item name="hintTextColor">@color/primary_green</item>
    </style><style name="AppCard" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">8dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:layout_margin">8dp</item>
    </style></file><file name="backup_rules" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\BookKeeping2025\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\BookKeeping2025\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\BookKeeping2025\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\BookKeeping2025\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\BookKeeping2025\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>