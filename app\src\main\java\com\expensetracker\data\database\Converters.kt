package com.expensetracker.data.database

import androidx.room.TypeConverter
import com.expensetracker.data.entity.PaymentMethod
import com.expensetracker.data.entity.RecurringType

class Converters {
    
    @TypeConverter
    fun fromPaymentMethod(paymentMethod: PaymentMethod): String {
        return paymentMethod.name
    }
    
    @TypeConverter
    fun toPaymentMethod(paymentMethod: String): PaymentMethod {
        return try {
            PaymentMethod.valueOf(paymentMethod)
        } catch (e: IllegalArgumentException) {
            PaymentMethod.CASH // Default fallback
        }
    }
    
    @TypeConverter
    fun fromRecurringType(recurringType: RecurringType?): String? {
        return recurringType?.name
    }
    
    @TypeConverter
    fun toRecurringType(recurringType: String?): RecurringType? {
        return if (recurringType != null) {
            try {
                RecurringType.valueOf(recurringType)
            } catch (e: IllegalArgumentException) {
                null
            }
        } else {
            null
        }
    }
}