package com.expensetracker.data.entity

import android.os.Parcelable
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.parcelize.Parcelize

@Parcelize
@Entity(tableName = "categories")
data class Category(
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "id")
    val id: Long = 0,
    
    @ColumnInfo(name = "name")
    val name: String,
    
    @ColumnInfo(name = "color")
    val color: String, // Hex color code (e.g., "#FF5722")
    
    @ColumnInfo(name = "icon")
    val icon: String? = null, // Icon resource name or emoji
    
    @ColumnInfo(name = "description")
    val description: String? = null,
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long = System.currentTimeMillis(),
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long = System.currentTimeMillis(),
    
    @ColumnInfo(name = "is_default")
    val isDefault: Boolean = false // For system default categories
) : Parcelable {
    
    companion object {
        // Default categories that will be created when the app first runs
        fun getDefaultCategories(): List<Category> {
            return listOf(
                Category(
                    name = "Food & Dining",
                    color = "#FF5722",
                    icon = "🍽️",
                    description = "Restaurants, groceries, and food delivery",
                    isDefault = true
                ),
                Category(
                    name = "Transportation",
                    color = "#2196F3",
                    icon = "🚗",
                    description = "Gas, public transport, taxi, and car maintenance",
                    isDefault = true
                ),
                Category(
                    name = "Entertainment",
                    color = "#9C27B0",
                    icon = "🎬",
                    description = "Movies, games, concerts, and leisure activities",
                    isDefault = true
                ),
                Category(
                    name = "Shopping",
                    color = "#FF9800",
                    icon = "🛍️",
                    description = "Clothing, electronics, and general shopping",
                    isDefault = true
                ),
                Category(
                    name = "Health & Medical",
                    color = "#4CAF50",
                    icon = "🏥",
                    description = "Doctor visits, medicine, and health expenses",
                    isDefault = true
                ),
                Category(
                    name = "Utilities",
                    color = "#607D8B",
                    icon = "⚡",
                    description = "Electricity, water, internet, and phone bills",
                    isDefault = true
                ),
                Category(
                    name = "Education",
                    color = "#3F51B5",
                    icon = "📚",
                    description = "Books, courses, tuition, and learning materials",
                    isDefault = true
                ),
                Category(
                    name = "Other",
                    color = "#795548",
                    icon = "📦",
                    description = "Miscellaneous expenses",
                    isDefault = true
                )
            )
        }
    }
}